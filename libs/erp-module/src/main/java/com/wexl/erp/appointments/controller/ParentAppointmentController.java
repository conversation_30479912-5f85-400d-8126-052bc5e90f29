package com.wexl.erp.appointments.controller;

import com.wexl.erp.appointments.dto.GatePassDto;
import com.wexl.erp.appointments.dto.ParentAppointmentDto;
import com.wexl.erp.appointments.service.AppointmentService;
import com.wexl.erp.appointments.service.GatePassService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/orgs/{orgSlug}/guardians/{guardianAuthId}/appointments")
public class ParentAppointmentController {

  private final AppointmentService appointmentService;
  private final GatePassService gatePassService;

  @PostMapping("/apply")
  public ParentAppointmentDto.Response applyForAppointment(
      @PathVariable String orgSlug,
      @PathVariable String guardianAuthId,
      @RequestBody ParentAppointmentDto.Request request) {
    try {
      return appointmentService.applyForAppointment(orgSlug, guardianAuthId, request);
    } catch (Exception e) {
      log.error("Error while applying for appointment: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @GetMapping
  public List<ParentAppointmentDto.Response> getAppliedAppointments(
      @PathVariable String orgSlug,
      @PathVariable String guardianAuthId,
      @RequestParam(required = false) String role) {
    try {
      return appointmentService.getGuardianAppointmentRequests(orgSlug, guardianAuthId, role);
    } catch (Exception e) {
      log.error("Error while fetching applied appointments: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PutMapping("/update/{appointmentId}")
  public ParentAppointmentDto.Response updateAppointment(
      @PathVariable String orgSlug,
      @PathVariable String guardianAuthId,
      @PathVariable Long appointmentId,
      @RequestBody ParentAppointmentDto.Request request) {
    try {
      return appointmentService.updateAppointmentRequest(
          orgSlug, guardianAuthId, appointmentId, request);
    } catch (Exception e) {
      log.error("Error while updating appointment: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @DeleteMapping("/delete/{appointmentId}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteAppointment(@PathVariable Long appointmentId) {
    try {
      appointmentService.deleteAppointmentRequest(appointmentId);
    } catch (Exception e) {
      log.error("Error while deleting appointment: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/gate-pass/apply")
  public GatePassDto.Response applyForGatePass(
      @PathVariable String orgSlug,
      @PathVariable String guardianAuthId,
      @RequestBody GatePassDto.Request request) {
    try {
      return gatePassService.applyForGatePass(orgSlug, guardianAuthId, request);
    } catch (Exception e) {
      log.error("Error while applying for gate pass: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PutMapping("/gate-pass/update/{gatePassId}")
  public GatePassDto.Response updateGatePass(
      @PathVariable String orgSlug,
      @PathVariable String guardianAuthId,
      @PathVariable Long gatePassId,
      @RequestBody GatePassDto.Request request) {
    try {
      return gatePassService.updateGatePassRequest(orgSlug, guardianAuthId, gatePassId, request);
    } catch (Exception e) {
      log.error("Error while updating gate pass: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }
}
