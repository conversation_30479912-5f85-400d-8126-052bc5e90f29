package com.wexl.retail.student.adaptivelearning.aiservice;

import com.wexl.retail.student.adaptivelearning.dto.AdaptiveLearningDto;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.spring.AiService;

@AiService
public interface AiMentor {

  @SystemMessage(
      "You are student mentor. You need to perform analysis of the answer selected by the students based on content provided")
  @UserMessage("{{userMessage}}")
  AdaptiveLearningDto.AdaptiveLearningAiResponse performExamAnalysis(String userMessage);
}
