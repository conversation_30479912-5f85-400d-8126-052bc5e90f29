package com.wexl.holisticreportcards.model;

import com.wexl.gilico.dto.ChildBetterTypes;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gillco_pre_primary_child_better_students")
public class ChildBetterStudents extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  private ChildBetterTypes term1;
  private ChildBetterTypes term2;

  @ManyToOne
  @JoinColumn(name = "gillco_pre_primary_child_better_id")
  private ChildBetter childBetter;
}
