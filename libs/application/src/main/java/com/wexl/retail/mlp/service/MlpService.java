package com.wexl.retail.mlp.service;

import static com.wexl.retail.commons.util.DateTimeUtil.getEpochFromStringDate;
import static com.wexl.retail.util.Constants.*;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.commons.util.RandomOptionGenerator;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.*;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.dto.QuestionDetails;
import com.wexl.retail.metrics.dto.QuestionsAnalytics;
import com.wexl.retail.mlp.dto.*;
import com.wexl.retail.mlp.model.Mlp;
import com.wexl.retail.mlp.model.MlpInst;
import com.wexl.retail.mlp.model.MlpItemStatus;
import com.wexl.retail.mlp.model.MlpItemType;
import com.wexl.retail.mlp.model.QuestionsAssigneeMode;
import com.wexl.retail.mlp.repository.MlpInstRepository;
import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.model.OrgTeacher;
import com.wexl.retail.model.PracticeRequest;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.model.UserDetails;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.organization.admin.StudentResponse;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.student.answer.StudentAnswerPracticeRequest;
import com.wexl.retail.student.answer.StudentAnswerService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.student.exam.ExamRequest;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.student.exam.ExamService;
import com.wexl.retail.student.mlp.service.MlpAssetHandler;
import com.wexl.retail.student.subject.profiles.dto.SubjectProfileDetailsResponse;
import com.wexl.retail.student.subject.profiles.service.SubjectProfilesService;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import com.wexl.retail.teacher.preferences.TeacherPreferences;
import com.wexl.retail.teacher.preferences.TeacherPreferencesResponse;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.user.dto.BasicUserInfo;
import com.wexl.retail.user.dto.OrganizationStrength;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.ExamUtils;
import com.wexl.retail.util.NotificationUtils;
import com.wexl.retail.util.StrapiService;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class MlpService {

  private static final String PUBLISHED = "PUBLISHED";
  private static final String ALL_STUDENTS = "ALL_STUDENTS";
  private static final String PRACTICE_COMPLETED = "PRACTICE_COMPLETED";
  private static final String IN_PROGRESS = "IN_PROGRESS";
  private static final String MLP_NOT_FOUND = "Mlp Response not found";
  private static final String NOT_STARTED = "NOT_STARTED";
  private static final String UNKNOWN = "UNKNOWN";
  private static final String EMPTY_STRING = "";
  private static final String VIMEO_URL_PREFIX = "https://player.vimeo.com";
  private static final String VIMEO_SOURCE = "WEXL_VIMEO";
  private static final String ALL_SECTIONS = "all-sections";
  private static final DecimalFormat format = Constants.DECIMAL_FORMAT;
  private final TeacherRepository teacherRepository;
  private final SectionRepository sectionRepository;
  private final UserRoleHelper userRoleHelper;
  private final MlpRepository mlpRepository;
  private final MlpInstRepository mlpInstRepository;

  private final StrapiService strapiService;
  private final DateTimeUtil dateTimeUtil;
  private final SectionService sectionService;
  private final StudentAuthService studentAuthService;
  private final StudentService studentService;
  private final AuthService authService;
  private final StudentRepository studentRepository;
  private final TeacherPreferences teacherPreferences;
  private final ContentService contentService;
  private final ExamService examService;
  private final OrganizationRepository organizationRepository;
  private final TeacherOrgsService teacherOrgsService;
  private final VideoUrlEncryptor urlEncryptor;
  private final ExamRepository examRepository;
  private final SubjectProfilesService subjectProfilesService;
  private final UserRepository userRepository;
  private final StudentAnswerService studentAnswerService;
  private final List<MlpAssetHandler> mlpAssetHandlers;
  private final RandomOptionGenerator randomOptionGenerator;

  private static final DecimalFormat decimalFormat = Constants.DECIMAL_FORMAT;

  private final MlpAttendanceService mlpAttendanceService;

  private final UserService userService;
  private final NotificationUtils notificationUtils;

  @Value("${app.switchOffGroupMlp}")
  private boolean switchOffGroupMlp;

  @Transactional
  public void createMlpLegacy(AdvancedMlpRequest mlpRequest, String orgSlug, String bearerToken) {

    List<String> sectionUuids = new ArrayList<>();
    if (mlpRequest.getSectionUuid() != null) {
      sectionUuids.add(mlpRequest.getSectionUuid());
    } else {
      sectionUuids.addAll(mlpRequest.getSectionUuids());
    }

    Teacher teacher = validateTeacher(mlpRequest.getTeacherId());
    List<Section> sections = validateSections(sectionUuids);

    List<String> questionUuids = new ArrayList<>();

    if (Objects.nonNull(mlpRequest.getSubtopicSlug())) {
      questionUuids =
          getPracticeQuestionsUsingSubtopic(
              mlpRequest.getSubjectSlug(),
              mlpRequest.getChapterSlug(),
              mlpRequest.getSubtopicSlug(),
              mlpRequest.getQuestionCount(),
              bearerToken);
    }

    for (Section section : sections) {
      var transMlp = createMlpSectionWise(teacher, section, mlpRequest, orgSlug);
      transMlp.setQuestionUuids(questionUuids);
      mlpRepository.save(transMlp);
    }
  }

  private Mlp createMlpSectionWise(
      Teacher teacher, Section section, AdvancedMlpRequest mlpRequest, String orgSlug) {
    final var transMlp = mlpRequestToMlp(mlpRequest, orgSlug);

    transMlp.setTeacher(teacher);
    transMlp.setSection(section);

    String refKey = UUID.randomUUID().toString();
    transMlp.setExamRef(refKey);
    mlpRepository.save(transMlp);
    if (!section.getOrganization().equals("wexl-internal")) {
      List<Student> students = getMlpStudents(mlpRequest, section);
      List<MlpInst> mlpInstRecords = new ArrayList<>();
      for (var student : students) {
        addMlpInstForStudent(student, transMlp);
      }

      mlpInstRepository.saveAll(mlpInstRecords);
      var testNameAndSubjectMessage =
          String.format(
              "MLP  %s  in  %s",
              ScheduleTestService.wrapQuotes(transMlp.getTitle()), transMlp.getSubjectName());
      var message = String.format(NOTIFICATION_MESSAGE, testNameAndSubjectMessage);
      var attributes =
          NotificationDto.TestAttributes.builder()
              .testId(transMlp.getId())
              .testName(transMlp.getTitle())
              .examRef(transMlp.getExamRef())
              .testType("MLP")
              .subject(transMlp.getSubjectName())
              .subtopic(transMlp.getSubtopicSlug())
              .build();
      notificationUtils.sendNotification(message, students, section.getOrganization(), attributes);
    }
    return transMlp;
  }

  private List<Student> getMlpStudents(AdvancedMlpRequest mlpRequest, Section section) {
    if (mlpRequest.getStudentIds() != null && !mlpRequest.getStudentIds().isEmpty()) {
      return studentRepository.findByIdInAndSectionAndDeletedAtIsNull(
          mlpRequest.getStudentIds(), section);
    }

    String boardSlug =
        mlpRequest.getBoardSlug() == null
            ? strapiService.getBoardUsingChapter(mlpRequest.getChapterSlug())
            : mlpRequest.getBoardSlug();
    var result =
        getStudentsWithProfile(
            mlpRequest.getSubjectSlug(), mlpRequest.getGradeSlug(), boardSlug, section.getId());

    if (result.isEmpty()) {
      return studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
    }

    return result;
  }

  private List<Student> getStudentsWithProfile(
      String subjectSlug, String gradeSlug, String boardSlug, long sectionId) {
    List<Student> students =
        studentRepository.getStudentsBySubjectProfileAndMlpRequest(
            gradeSlug, boardSlug, subjectSlug, sectionId);
    if (students.isEmpty()) {
      return new ArrayList<>();
    }
    return students;
  }

  @Transactional
  public void createMlpAdvanced(AdvancedMlpRequest advancedMlpRequest, String orgSlug) {

    if (Objects.isNull(advancedMlpRequest.getQuestionUuids())
        || advancedMlpRequest.getQuestionUuids().isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.QuestionUUIDsNotBeNullOrEmpty");
    }
    Teacher teacher = validateTeacher(advancedMlpRequest.getTeacherId());
    List<Section> sections = validateSections(advancedMlpRequest.getSectionUuids());
    for (Section section : sections) {
      Mlp mlp = createMlpSectionWise(teacher, section, advancedMlpRequest, orgSlug);
      mlp.setQuestionUuids(advancedMlpRequest.getQuestionUuids());
      mlpRepository.save(mlp);
    }
  }

  public Teacher validateTeacher(Long teacherId) {
    Optional<Teacher> optionalTeacher = teacherRepository.findById(teacherId);
    if (optionalTeacher.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeacherNotFound");
    }
    return optionalTeacher.get();
  }

  private List<Section> validateSections(List<String> sectionUuids) {
    List<Section> sections = new ArrayList<>();
    for (String sectionUuid : sectionUuids) {
      Optional<Section> optionalSection =
          sectionRepository.findByUuid(UUID.fromString(sectionUuid));
      if (optionalSection.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.SectionNotFound",
            new String[] {sectionUuid});
      }
      sections.add(optionalSection.get());
    }
    return sections;
  }

  private Mlp mlpRequestToMlp(MlpRequest mlpRequest, String orgSlug) {

    Entity chapter = null;
    Entity synopsis = null;
    try {
      chapter = strapiService.getChapterBySlug(mlpRequest.getChapterSlug());
      synopsis = strapiService.getAssetBySlug(mlpRequest.getSynopsisSlug());
    } catch (Exception ex) {
      log.error("Unable to fetch metadata", ex);
    }

    Mlp transMlp = new Mlp();

    transMlp.setOrgSlug(orgSlug);
    transMlp.setChapterSlug(mlpRequest.getChapterSlug());
    transMlp.setSubtopicSlug(mlpRequest.getSubtopicSlug());
    transMlp.setSubjectSlug(mlpRequest.getSubjectSlug());
    transMlp.setVideoSlug(mlpRequest.getVideoSlug());
    transMlp.setAltVideoSlug(mlpRequest.getAltVideoSlug());
    transMlp.setSynopsisSlug(mlpRequest.getSynopsisSlug());
    transMlp.setGradeSlug(mlpRequest.getGradeSlug());
    transMlp.setDayOfWeek(mlpRequest.getDayOfWeek());
    transMlp.setQuestionCount(mlpRequest.getQuestionCount());
    transMlp.setTitle(mlpRequest.getTitle());
    transMlp.setVideoSource(mlpRequest.getVideoSource());
    transMlp.setComment(mlpRequest.getDescription());
    transMlp.setSynopsisName(synopsis == null ? EMPTY_STRING : synopsis.getAssetName());
    transMlp.setChapterName(chapter == null ? EMPTY_STRING : chapter.getName());
    transMlp.setGradeName(contentService.getGradeNameBySlug(mlpRequest.getGradeSlug()));
    transMlp.setSubtopicName(strapiService.getSubTopicNameBySlug(mlpRequest.getSubtopicSlug()));
    transMlp.setSubjectName(strapiService.getSubjectNameBySlug(mlpRequest.getSubjectSlug()));
    transMlp.setQuestionsAssigneeMode(mlpRequest.getQuestionsAssigneeMode());
    transMlp.setShaLink(mlpRequest.getShaLink());
    return transMlp;
  }

  public List<MlpResponse> getMlpsByTeacherPreferences(String orgId, int limit) {
    Pageable pageable = PageRequest.of(0, limit);
    User teacheUser = authService.getTeacherDetails();
    if (AuthUtil.isOrgAdmin(teacheUser)) {
      Set<Section> sections =
          sectionRepository.findAllByOrganizationAndDeletedAtIsNullOrderByName(orgId);
      return getMlpResponses(
          mlpRepository.findAllByOrgSlugAndSectionInOrderByCreatedAtDesc(
              teacheUser.getOrganization(), new ArrayList<>(sections), pageable));
    }
    TeacherPreferencesResponse teacherPreferencesResponse =
        teacherPreferences.getTeacherPreferences();
    return getMlpResponses(
        mlpRepository.getMlpsByTeacherPreferences(
            orgId,
            teacherPreferencesResponse.getSubjects(),
            teacherPreferencesResponse.getSections(),
            limit));
  }

  public List<MlpResponse> getMlpResponses(List<Mlp> mlps) {
    List<MlpResponse> mlpResponseList = new ArrayList<>();
    mlps.forEach(mlp -> mlpResponseList.add(mlpToMlpResponse(mlp)));
    return mlpResponseList;
  }

  private MlpResponse mlpToMlpResponse(Mlp mlp) {
    return MlpResponse.builder()
        .id(mlp.getId())
        .chapter(mlp.getChapterSlug())
        .examRef(mlp.getExamRef())
        .teacherName(
            mlp.getTeacher().getUserInfo().getFirstName()
                + " "
                + mlp.getTeacher().getUserInfo().getLastName())
        .questionCount(mlp.getQuestionCount())
        .subTopic(mlp.getSubtopicSlug())
        .synopsisSlug(mlp.getSynopsisSlug())
        .videoSlug(mlp.getVideoSlug())
        .altVideoSlug(urlEncryptor.convertPlain(mlp.getAltVideoSlug()))
        .videoSha(mlp.getShaLink())
        .videoSource(getVideoSource(mlp.getVideoSource(), mlp.getAltVideoSlug()))
        .source(getVideoSource(mlp.getVideoSource(), mlp.getAltVideoSlug()))
        .subject(mlp.getSubjectSlug())
        .subTopicName(mlp.getSubtopicName())
        .synopsisName(mlp.getSynopsisName())
        .gradeName(mlp.getGradeName())
        .chapterName(mlp.getChapterName())
        .createdAt(
            Objects.nonNull(mlp.getCreatedAt())
                ? DateTimeUtil.convertIso8601ToEpoch(mlp.getCreatedAt().toLocalDateTime())
                : null)
        .title(mlp.getTitle())
        .sectionName(mlp.getSection().getName())
        .comment(mlp.getComment())
        .questionsAssigneeMode(mlp.getQuestionsAssigneeMode())
        .attendancePercentage(mlp.getAttendancePercentage())
        .knowledgePercentage(mlp.getKnowledgePercentage())
        .build();
  }

  public String getSubTopicName(String subTopicSlug) {
    if (Objects.nonNull(subTopicSlug)) {
      return strapiService.getSubTopicBySlug(subTopicSlug).getName();
    }
    return EMPTY_STRING;
  }

  public void validateMlp(Long mlpId) {
    Optional<Mlp> mlp = mlpRepository.findById(mlpId);

    if (mlp.isEmpty()) {
      log.error(MLP_NOT_FOUND);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MLPNotFound");
    }

    Mlp selectedMlp = mlp.get();
    List<Student> students = studentRepository.getStudentsBySection(selectedMlp.getSection());

    List<MlpInst> mlpInsts = selectedMlp.getMlpInsts();
    Set<Long> studentIds =
        mlpInsts.stream().map(i -> i.getStudent().getId()).collect(Collectors.toSet());
    Set<String> studentsWithoutMlps = new HashSet<>();
    students.forEach(
        ss -> {
          long studentId = ss.getId();
          if (!studentIds.contains(studentId)) {
            studentsWithoutMlps.add(ss.getUserInfo().getAuthUserId());
          }
        });

    if (!studentsWithoutMlps.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentsNotAssignedMlp",
          new String[] {String.join(",", studentsWithoutMlps)});
    }
  }

  public MlpSummary getMlpStudentPracticeDetails(Long mlpId, String orgId) throws ApiException {
    Optional<Mlp> optionalMlp = mlpRepository.findById(mlpId);
    if (optionalMlp.isEmpty()) {
      log.error(MLP_NOT_FOUND);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MlpFind.MLPId");
    }
    List<Mlp> mlp = new ArrayList<>();
    List<Long> allMlps = new ArrayList<>();
    if (Objects.nonNull(orgId)) {
      mlp = mlpRepository.findByParentAndOrgSlug(optionalMlp.get(), orgId);
      if (mlp.isEmpty()) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "error.MlpNotFound.ForChildOrg",
            new String[] {orgId});
      }
    } else {
      mlp.add(optionalMlp.get());
      allMlps = getChildMlpsIfExist(mlpId);
    }
    var data = mlp.stream().map(Mlp::getId).toList();
    allMlps.addAll(data);
    List<MlpInst> mlpInsts = mlpInstRepository.getAllMlpInst(allMlps);

    MlpSummary mlpSummary = buildMlpSummary(mlp.getFirst());
    if (mlpInsts.isEmpty()) {
      return mlpSummary;
    }
    List<MlpInst> filteredMlpInsts =
        mlpInsts.stream()
            .filter(mi -> mi.getStudent().getUserInfo().getDeletedAt() == null)
            .toList();
    List<MlpStudentDetail> mlpResponse = new ArrayList<>();

    filteredMlpInsts.forEach(
        mlpInst -> {
          var response = new MlpStudentDetail();

          var studentUser = mlpInst.getStudent().getUserInfo();
          response.setStudentId(mlpInst.getStudent().getId());
          response.setPracticeStatus(NOT_STARTED);
          response.setUserName(
              Objects.nonNull(mlpInst.getStudent().getUserInfo())
                  ? mlpInst.getStudent().getUserInfo().getUserName()
                  : "");
          response.setSynopsisStatus(mlpInst.getSynopsisStatus());
          response.setVideoStatus(mlpInst.getVideoStatus());
          response.setFullName(userService.getNameByUserInfo(studentUser));
          var orgSlug = studentUser.getOrganization();
          var orgName = organizationRepository.findBySlug(orgSlug);
          response.setOrgName(orgName.getName());
          response.setOrgSlug(orgSlug);

          if (mlpInst.getExam() != null
              && mlpInst.getExam().getTotalMarks() != null
              && mlpInst.getExam().getTotalMarks() != 0
              && mlpInst.getExam().getNoOfQuestions() != 0) {
            buildDetailedStudentMlpResponse(response, mlpInst.getExam());
          }

          mlpResponse.add(response);
        });

    MlpPracticeVideoSynopsisCount mlpPracticeVideoSynopsisCount =
        mlpInstRepository.getAttemptedVideoAndSynopsisCount(allMlps);
    int noOfAttemptedStudents = mlpPracticeVideoSynopsisCount.getPracticeCount();
    int noOfNotAttemptedStudents = filteredMlpInsts.size() - noOfAttemptedStudents;
    int attemptedSynopsisCount = mlpPracticeVideoSynopsisCount.getSynopsisCount();
    int notAttemptedSynopsisCount = filteredMlpInsts.size() - attemptedSynopsisCount;
    int attemptedVideoCount = mlpPracticeVideoSynopsisCount.getVideoCount();
    int notAttemptedVideoCount = filteredMlpInsts.size() - attemptedVideoCount;
    mlpSummary.setAttempted(noOfAttemptedStudents);
    mlpSummary.setAttemptedSynopsis(attemptedSynopsisCount);
    mlpSummary.setNotAttemptedSynopsis(notAttemptedSynopsisCount);
    mlpSummary.setAttemptedVideo(attemptedVideoCount);
    mlpSummary.setNotAttemptedVideo(notAttemptedVideoCount);
    mlpSummary.setNotAttempted(noOfNotAttemptedStudents);
    mlpSummary.setDetailedStudentMlpResponses(sortedMlpResponse(mlpResponse));
    mlpSummary.setAverage(((float) (noOfAttemptedStudents * 100) / filteredMlpInsts.size()));
    mlpSummary.setTotalStudents(filteredMlpInsts.size());
    return mlpSummary;
  }

  private List<MlpStudentDetail> sortedMlpResponse(List<MlpStudentDetail> mlpStudentDetails) {
    return mlpStudentDetails.stream()
        .sorted(
            Comparator.comparing(MlpStudentDetail::getPracticeStatus, Comparator.reverseOrder()))
        .toList();
  }

  private List<Long> getChildMlpsIfExist(Long mlpId) {
    final List<Long> allChildMlpIds = mlpRepository.getAllChildMlpIds(mlpId);
    if (allChildMlpIds == null) {
      return new ArrayList<>();
    }
    return allChildMlpIds;
  }

  public float getPercentageOfWholeClass(List<MlpInst> mlpInsts, int noOfAttemptedStudents) {
    float totalPercentage = 0f;
    for (MlpInst mlpInst : mlpInsts) {
      if (mlpInst.getExam() != null
          && mlpInst.getExam().getTotalMarks() != null
          && mlpInst.getExam().getTotalMarks() != 0
          && mlpInst.getExam().getNoOfQuestions() != 0) {
        totalPercentage = totalPercentage + ExamUtils.setPercentage(mlpInst.getExam());
      }
    }
    if (noOfAttemptedStudents != 0) {
      return Float.parseFloat(
          Constants.DECIMAL_FORMAT.format(totalPercentage / noOfAttemptedStudents));
    }
    return 0f;
  }

  public void buildDetailedStudentMlpResponse(MlpStudentDetail mlpStudentDetail, Exam exam) {
    Student student = studentService.getStudentById(exam.getStudentId());
    mlpStudentDetail.setUserName(student.getUserInfo().getUserName());
    mlpStudentDetail.setExamId(exam.getId());
    mlpStudentDetail.setStudentId(exam.getStudentId());
    mlpStudentDetail.setMarksScored(exam.getMarksScored());
    mlpStudentDetail.setTotalMarks(exam.getTotalMarks());
    mlpStudentDetail.setStartTime(
        DateTimeUtil.convertIso8601ToEpoch(exam.getStartTime().toLocalDateTime()));
    mlpStudentDetail.setPercentage(
        Float.parseFloat(
            Constants.DECIMAL_FORMAT.format(exam.getMarksScored() / exam.getTotalMarks() * 100)));
    if (exam.getEndTime() == null) {
      mlpStudentDetail.setPracticeStatus(IN_PROGRESS);
      mlpStudentDetail.setEndTime(0L);
    } else {
      mlpStudentDetail.setPracticeStatus(PRACTICE_COMPLETED);
      mlpStudentDetail.setEndTime(
          DateTimeUtil.convertIso8601ToEpoch(exam.getEndTime().toLocalDateTime()));
    }
  }

  public MlpSummary buildMlpSummary(Mlp mlp) {
    Grade grade = null;
    Entity chapter = null;
    Entity subject = null;
    Entity synopsis = null;

    try {
      grade = contentService.getGradeBySlug(mlp.getGradeSlug());
      chapter = strapiService.getChapterBySlug(mlp.getChapterSlug());
      subject = strapiService.getSubjectBySlug(mlp.getSubjectSlug());
      synopsis = strapiService.getAssetBySlug(mlp.getSynopsisSlug());
    } catch (Exception ex) {
      log.error("Unable to fetch metadata for mlp with id [" + mlp.getId() + "]");
    }

    return MlpSummary.builder()
        .title(mlp.getTitle())
        .chapterName(getEntityName(chapter))
        .gradeName(getGradeEntityName(grade))
        .subjectName(getEntityName(subject))
        .sectionName(mlp.getSection().getName())
        .teacherName(
            mlp.getTeacher().getUserInfo().getFirstName()
                + " "
                + mlp.getTeacher().getUserInfo().getLastName())
        .subTopicName(mlp.getSubtopicName())
        .chapterId(getEntityId(chapter))
        .gradeId(getGradeEntityId(grade))
        .subjectId(getEntityId(subject))
        .subTopicSlug(mlp.getSubtopicSlug())
        .videoSlug(mlp.getVideoSlug())
        .altVideoSlug(urlEncryptor.convertPlain(mlp.getAltVideoSlug()))
        .videoSha(urlEncryptor.convertEncrypted(mlp.getAltVideoSlug()))
        .videoSource(getVideoSource(mlp.getVideoSource(), mlp.getAltVideoSlug()))
        .examRef(mlp.getExamRef())
        .synopsisSlug(mlp.getSynopsisSlug())
        .synopsisName(synopsis == null ? UNKNOWN : synopsis.getAssetName())
        .questionCount(mlp.getQuestionCount())
        .createdAt(
            Objects.nonNull(mlp.getCreatedAt())
                ? DateTimeUtil.convertIso8601ToEpoch(mlp.getCreatedAt().toLocalDateTime())
                : null)
        .organizationName(organizationRepository.findBySlug(mlp.getOrgSlug()).getName())
        .build();
  }

  private String getVideoSource(String videoSource, String altVideoSlug) {
    // this is a hacky way to find out source due to a bug introduced in 1.0.23.
    // this will be removed later
    if (StringUtils.isNotBlank(videoSource)) {
      return videoSource;
    }
    String plainUrl = urlEncryptor.convertPlain(altVideoSlug);
    if (StringUtils.isNotBlank(plainUrl) && plainUrl.startsWith(VIMEO_URL_PREFIX)) {
      return VIMEO_SOURCE;
    }
    return videoSource;
  }

  private int getEntityId(Entity entity) {
    return entity == null ? 0 : entity.getId();
  }

  private String getEntityName(Entity entity) {
    return entity == null ? UNKNOWN : entity.getName();
  }

  private String getGradeEntityName(Grade entity) {
    return entity == null ? UNKNOWN : entity.getName();
  }

  private int getGradeEntityId(Grade entity) {
    return entity == null ? 0 : entity.getId();
  }

  public List<MlpResponse> getMlpsAssignedToStudent(
      String orgId, String userName, Long date, int limit, String mlpStatus) {
    User studentUser = userRepository.getUserByAuthUserId(userName);
    if (Objects.isNull(studentUser) || !studentUser.getOrganization().equals(orgId)) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.MLPResponseNotAuthorized");
    }

    Student student = studentUser.getStudentInfo();
    String gradeSlug = student.getSection().getGradeSlug();
    if (Objects.nonNull(date)) {
      return buildStudentMlpResponses(
          mlpRepository.getStudentMlpReponseByDate(
              dateTimeUtil.repositoryFriendlyDateFromEpoch(date), student.getId(), gradeSlug));
    }
    if (COMPLETED.equals(mlpStatus)) {
      return buildStudentMlpResponses(
          mlpRepository.getCompletedMlpResponses(student.getId(), limit));
    }

    return buildStudentMlpResponses(mlpRepository.getPendingMlpReponses(student.getId(), limit));
  }

  private List<MlpResponse> buildStudentMlpResponses(
      List<StudentResponseMlpQueryResult> responses) {
    return responses.stream()
        .map(
            response ->
                MlpResponse.builder()
                    .id(response.getId())
                    .chapter(response.getChapterSlug())
                    .examRef(response.getExamRef())
                    .teacherName(response.getTeacherName())
                    .questionCount(response.getQuestionCount())
                    .subTopic(response.getSubtopicSlug())
                    .synopsisSlug(response.getSynopsisSlug())
                    .videoSlug(response.getVideoSlug())
                    .videoSource(
                        getVideoSource(response.getVideoSource(), response.getAltVideoSlug()))
                    .altVideoSlug(urlEncryptor.convertPlain(response.getAltVideoSlug()))
                    .videoSha(response.getShaLink())
                    .subject(response.getSubjectSlug())
                    .subTopicName(response.getSubtopicName())
                    .synopsisName(response.getSynopsisName())
                    .gradeName(response.getGradeName())
                    .chapterName(response.getChapterName())
                    .createdAt(
                        Objects.nonNull(response.getCreatedAt())
                            ? DateTimeUtil.convertIso8601ToEpoch(
                                response.getCreatedAt().toLocalDateTime())
                            : null)
                    .title(response.getTitle())
                    .sectionName(response.getSectionName())
                    .comment(response.getComment())
                    .synopsisStatus(response.getSynopsisStatus())
                    .videoStatus(response.getVideoStatus())
                    .practiceStatus(response.getPracticeStatus())
                    .questionsAssigneeMode(
                        QuestionsAssigneeMode.valueOf(response.getQuestionsAssigneeMode()))
                    .examId(response.getExamId())
                    .subject(response.getSubjectSlug())
                    .subjectName(response.getSubjectName())
                    .subjectName(response.getSubjectName())
                    .build())
        .toList();
  }

  public List<Long> getMlpActivityDates(String orgSlug, String studentUserName) {
    StudentResponse student = getStudent(orgSlug, studentUserName);
    Section section = sectionService.getSectionByNameAndOrg(student.getSection(), orgSlug);
    if (section == null) {
      return new ArrayList<>();
    }
    List<String> timestamps =
        mlpRepository.getActivityDatesForSection(orgSlug, student.getGradeSlug(), section.getId());
    return timestamps.stream()
        .map(timestamp -> DateTimeUtil.convertIso8601ToEpoch(parseTimeStamp(timestamp)))
        .toList();
  }

  private LocalDateTime parseTimeStamp(String timestamp) {
    DateTimeFormatter parseFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    try {
      return LocalDate.parse(timestamp, parseFormatter).atStartOfDay();
    } catch (Exception ex) {
      log.error(
          "Unable to parse the timestamp [" + timestamp + "] retrieved from the database", ex);
    }
    return LocalDateTime.now();
  }

  private StudentResponse getStudent(String orgSlug, String studentUserName) {
    StudentResponse student;
    try {
      student = studentAuthService.getStudent(orgSlug, studentUserName);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentFind.Organization",
          new String[] {studentUserName, orgSlug},
          e);
    }
    return student;
  }

  public void updateMlpInstStatus(MlpStudentStatus mlpStudentStatusUpdate) {
    var student = authService.getStudentDetails().getStudentInfo();
    long studentId = student.getId();

    Optional<Mlp> mlp = mlpRepository.findFirstByExamRef(mlpStudentStatusUpdate.getUuid());
    if (mlp.isEmpty()) {
      return;
    }
    try {
      var mlpData = mlp.get();
      var mlpInst =
          mlpData.getMlpInsts().stream()
              .filter(s -> s.getStudent().getId() == studentId)
              .findFirst();
      if (mlpInst.isEmpty()) {
        addMlpInstForStudent(student, mlpData);
      }
      Optional<MlpInst> optionalMlpInst =
          mlpInstRepository.getMlpInstByMlpAndStudent(mlpData, student);
      if (optionalMlpInst.isEmpty()) {
        return;
      }
      var mlpInstdata = optionalMlpInst.get();
      if (MlpItemType.VIDEO.equals(mlpStudentStatusUpdate.getItemType())) {
        mlpInstdata.setVideoStatus(mlpStudentStatusUpdate.getItemStatus());
      } else if (MlpItemType.SYNOPSIS.equals(mlpStudentStatusUpdate.getItemType())) {
        mlpInstdata.setSynopsisStatus(mlpStudentStatusUpdate.getItemStatus());
      }
      var studentPercentage =
          calculateStudentAttendanceAndKnowledgePercentage(mlpData, studentId, mlpInstdata);
      mlpInstdata.setKnowledgePercentage(studentPercentage.getKnowledgePercentage());
      mlpInstdata.setAttendancePercentage(studentPercentage.getAttendancePercentage());
      var totalPercentage = calculateTotalMlpPercentage(mlpData);
      mlpData.setAttendancePercentage(totalPercentage.getAttendancePercentage());
      mlpData.setKnowledgePercentage(totalPercentage.getKnowledgePercentage());

      mlpRepository.save(mlpData);
      mlpAttendanceService.updateMlpAttendance(mlpData, student);
    } catch (Exception ex) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND,
          "error.UnableToUpdateItemStatus",
          new String[] {
            mlpStudentStatusUpdate.getItemType().name(),
            mlpStudentStatusUpdate.getItemStatus().name()
          },
          ex);
    }
  }

  private Double getMlpAttributeCount(Mlp mlp) {
    double count = 0;
    if (mlp.getSynopsisSlug() != null) {
      count = count + 1;
    }
    if (mlp.getQuestionCount() != null) {
      count = count + 1;
    }
    if (mlp.getVideoSlug() != null) {
      count = count + 1;
    }
    return count;
  }

  private Integer isSynopsisCompletedByStudent(Mlp mlp, MlpInst mlpInst) {
    if (mlp.getSynopsisSlug() != null && mlpInst.getSynopsisStatus() == MlpItemStatus.COMPLETED) {
      return 1;
    }
    return 0;
  }

  private Integer isVideoCompletedByStudent(Mlp mlp, MlpInst mlpInst) {
    if (mlp.getVideoSlug() != null && mlpInst.getVideoStatus() == MlpItemStatus.COMPLETED) {
      return 1;
    }
    return 0;
  }

  private Integer isPracticeCompletedByStudent(Mlp mlp, Long studentId, String examRef) {
    if (mlp.getQuestionCount() != null) {
      var examsData = examRepository.findByStudentIdAndExamRef(studentId, examRef);
      if (examsData.isPresent()) {
        return 1;
      }
    }
    return 0;
  }

  private Double calculateStudentKnowledgePercentage(Mlp mlp, Long studentId, String examRef) {
    double knowledgePercentage = 0.0;
    if (mlp.getQuestionCount() != null) {
      var exam = examRepository.findByStudentIdAndExamRef(studentId, examRef);
      if (exam.isPresent()) {
        var examData = exam.get();
        double correctAnswers =
            examData.getCorrectAnswers() == null ? 0.0 : examData.getCorrectAnswers();
        double noOfQuestions = examData.getNoOfQuestions();
        knowledgePercentage =
            (noOfQuestions == 0.0
                ? 0.0
                : Double.parseDouble(format.format((correctAnswers / noOfQuestions) * 100)));
      }
    }
    return knowledgePercentage;
  }

  private Double calculateStudentAttendancePercentage(
      Mlp mlp, MlpInst mlpInst, Long studentId, String examRef, Double totalCount) {
    var synopsisStatus = isSynopsisCompletedByStudent(mlp, mlpInst);
    var videoStatus = isVideoCompletedByStudent(mlp, mlpInst);
    var practiceStatus = isPracticeCompletedByStudent(mlp, studentId, examRef);
    Integer attendedCount = synopsisStatus + videoStatus + practiceStatus;
    return Double.parseDouble(format.format((attendedCount / totalCount) * 100));
  }

  private List<MlpKnowledgeAndAttendanceData> getMlpKnowledgeAndAttendaceAverage(Mlp mlpData) {
    return mlpData.getMlpInsts().stream()
        .map(
            s ->
                MlpKnowledgeAndAttendanceData.builder()
                    .attendancePercentage(s.getAttendancePercentage())
                    .knowledgePercentage(s.getKnowledgePercentage())
                    .build())
        .toList();
  }

  private double calculateMlpKnowledgePercentage(
      List<MlpKnowledgeAndAttendanceData> mlpKnowledgeData) {
    if (mlpKnowledgeData.isEmpty()) {
      return 0.0;
    }
    Double totalKnowledgePercentage = 0.0;

    for (var d : mlpKnowledgeData) {
      if (d.getKnowledgePercentage() != null) {
        totalKnowledgePercentage += d.getKnowledgePercentage();
      }
    }
    return Double.parseDouble(format.format(totalKnowledgePercentage / mlpKnowledgeData.size()));
  }

  public double calculateMlpAttendancePercentage(
      List<MlpKnowledgeAndAttendanceData> mlpAttendaceData) {
    if (mlpAttendaceData.isEmpty()) {
      return 0.0;
    }
    Double totalAttendancePercentage = 0.0;

    for (var d : mlpAttendaceData) {
      if (d.getAttendancePercentage() != null) {
        totalAttendancePercentage += d.getAttendancePercentage();
      }
    }
    return Double.parseDouble(format.format(totalAttendancePercentage / mlpAttendaceData.size()));
  }

  public QuestionResponse getMlpQuestions(
      String examRef,
      String bearerToken,
      Boolean withAnswerAndExplaination,
      Boolean isMlpAnalytics) {

    Optional<Mlp> optionalMlp = mlpRepository.getFirstByExamRef(examRef);
    if (optionalMlp.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MLPFind.ExamRef");
    }
    var mlp = optionalMlp.get();

    /*
    Deprecated Code. Once all auto mode mlps have question uuids we can remove this code.
     */
    if (mlp.getQuestionsAssigneeMode().equals(QuestionsAssigneeMode.AUTO)
            && (Objects.isNull(mlp.getQuestionUuids()))
        || mlp.getQuestionUuids().isEmpty()) {
      Entity chapter;
      Entity subject;
      try {
        chapter = strapiService.getChapterBySlug(mlp.getChapterSlug());
        subject = strapiService.getSubjectBySlug(mlp.getSubjectSlug());
      } catch (Exception ex) {
        throw new ApiException(
            InternalErrorCodes.NO_RECORD_FOUND,
            "error.MLPMetaDataFind.MLPID",
            new String[] {Long.toString(mlp.getId())},
            ex);
      }
      PracticeRequest practiceRequest =
          PracticeRequest.builder()
              .chapter(chapter.getId())
              .goalComplexity(1)
              .offset(0)
              .subject(subject.getId())
              .questionCount(mlp.getQuestionCount())
              .subTopic(mlp.getSubtopicSlug())
              .build();
      return contentService.getPracticeQuestions(bearerToken, practiceRequest);
    }
    return getQuestionsByUuidsAndSubjectSlug(
        mlp.getQuestionUuids(),
        mlp.getSubjectSlug(),
        bearerToken,
        withAnswerAndExplaination,
        isMlpAnalytics);
  }

  public QuestionResponse getQuestionsByUuidsAndSubjectSlug(
      List<String> questionUuids,
      String subjectSlug,
      String bearerToken,
      Boolean withAnswerAndExplaination,
      Boolean isMlpAnalytics) {
    List<Question> questions = new ArrayList<>();

    if (Boolean.TRUE.equals(withAnswerAndExplaination)) {
      for (String qUuid : questionUuids) {
        questions.add(
            contentService.getQuestionBySubjectSlugAndUuid(
                bearerToken, QuestionType.MCQ, subjectSlug, qUuid, Boolean.TRUE));
      }
    } else {
      for (String qUuid : questionUuids) {
        questions.add(
            contentService.getQuestionBySubjectSlugAndUuidForStudent(
                bearerToken, QuestionType.MCQ, subjectSlug, qUuid, Boolean.TRUE));
      }
    }
    if (questions.isEmpty()) {
      return QuestionResponse.builder().message("No Data Found").build();
    }
    Collections.shuffle(questions);
    shuffleOptions(questions, isMlpAnalytics);
    return QuestionResponse.builder().data(questions).message("Success").status(true).build();
  }

  private List<Question> shuffleOptions(List<Question> questions, boolean isMlpAnalytics) {
    if (isMlpAnalytics) {
      return questions;
    }
    List<Question> response = new ArrayList<>();
    questions.forEach(
        question -> {
          Map<String, String> optionMap = new HashMap<>();
          optionMap.put("A", question.getOption1());
          optionMap.put("B", question.getOption2());
          optionMap.put("C", question.getOption3());
          optionMap.put("D", question.getOption4());

          var randomOptionPicker = randomOptionGenerator.randomOptionPicker();
          List<String> shuffledOrder = randomOptionPicker.values().iterator().next();
          question.setOption1(optionMap.get(shuffledOrder.get(0)));
          question.setOption2(optionMap.get(shuffledOrder.get(1)));
          question.setOption3(optionMap.get(shuffledOrder.get(2)));
          question.setOption4(optionMap.get(shuffledOrder.get(3)));
          question.setPermKey(randomOptionPicker.keySet().iterator().next());
          response.add(question);
        });
    return response;
  }

  public ExamResponse startMlpExam(
      Student student, String orgSlug, String examRef, String bearerToken) {

    var exam = examRepository.findByStudentAndRef(student, examRef);

    if (!exam.isEmpty() && exam.getFirst().isCompleted()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.RestrictMutlipleMlpAttempts");
    }

    Optional<Mlp> optionalMlp = mlpRepository.getFirstByExamRef(examRef);
    if (optionalMlp.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MLPFind.ExamRef");
    }
    var mlp = optionalMlp.get();

    var subTopic = contentService.getSubTopicBySlug(orgSlug, mlp.getSubtopicSlug(), bearerToken);
    ExamRequest examRequest =
        ExamRequest.builder()
            .uuid(examRef)
            .examType(PRACTICE_EXAM)
            .chapterId(subTopic.getChapterId())
            .classId(subTopic.getGradeId())
            .examDifficultyLevelId(1)
            .subjectId(subTopic.getSubjectId())
            .subTopicId(subTopic.getId())
            .isAllChapterSelected(false)
            .build();
    return examService.createExamPractice(student, examRequest, subTopic);
  }

  public List<MlpTeacherMetricResponse> getTeacherMetrics(
      String orgSlug, Long fromDate, String gradeSlug) {

    List<MlpTeacherMetrics> mlpTeacherMetrics =
        mlpRepository.getMlpTeacherMetrics(
            orgSlug, dateTimeUtil.repositoryFriendlyDateFromEpoch(fromDate), gradeSlug);
    return mlpTeacherMetrics.stream()
        .map(
            m ->
                MlpTeacherMetricResponse.builder()
                    .name(m.getName())
                    .gradeName(m.getGradeName())
                    .gradeSlug(m.getGradeSlug())
                    .count(m.getMlpCount())
                    .sectionName(m.getSectionName())
                    .averageAttendance(
                        Double.parseDouble(
                            Constants.DECIMAL_FORMAT.format(m.getAverageAttendance())))
                    .build())
        .toList();
  }

  public List<MlpGradeMetricResponse> getGradeMetrics(String orgId, Long fromDate) {
    List<MlpGradeMetrics> mlpGradeMetrics =
        mlpRepository.getMlpGradeMetrics(
            orgId, dateTimeUtil.repositoryFriendlyDateFromEpoch(fromDate));
    return mlpGradeMetrics.stream()
        .map(
            m ->
                MlpGradeMetricResponse.builder()
                    .gradeName(m.getName())
                    .count(m.getMlpCount())
                    .build())
        .toList();
  }

  public List<MlpResponse> getAllMlps(String orgId, @NonNull Long fromDate, int limit) {
    String fromDateInString = dateTimeUtil.repositoryFriendlyDateFromEpoch(fromDate);
    List<Mlp> mlps = mlpRepository.getAllMlps(orgId, fromDateInString, limit);
    return getMlpResponses(mlps);
  }

  public List<String> getPracticeQuestionsUsingSubtopic(
      String subjectSlug,
      String chapterSlug,
      String subtopicSlug,
      int questionCount,
      String bearerToken) {

    Entity chapter;
    Entity subject;
    try {
      chapter = strapiService.getChapterBySlug(chapterSlug);
      subject = strapiService.getSubjectBySlug(subjectSlug);
    } catch (Exception ex) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND,
          "error.MLPMetaDataFind.Slugs",
          new String[] {String.join(",", chapterSlug), String.join(",", subjectSlug)},
          ex);
    }
    PracticeRequest practiceRequest =
        PracticeRequest.builder()
            .chapter(chapter.getId())
            .goalComplexity(1)
            .offset(0)
            .subject(subject.getId())
            .questionCount(questionCount)
            .subTopic(subtopicSlug)
            .build();
    QuestionResponse questionResponse =
        contentService.getPracticeQuestions(bearerToken, practiceRequest);

    if (questionResponse.getData() == null || questionResponse.getData().isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.FetchQuestionsFind.SubjectSlug",
          new String[] {subjectSlug});
    }

    return questionResponse.getData().stream().map(Question::getUuid).toList();
  }

  public List<MlpMetricResponse> getChildInstitutionMetrics(Long fromDate) {
    User user = authService.getUserDetails();
    List<Organization> childOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());

    List<String> childOrgSlugs = childOrgs.stream().map(Organization::getSlug).toList();

    List<MlpGradeMetrics> mlpGradeMetrics =
        mlpRepository.getChildInstitutionMetrics(
            childOrgSlugs, dateTimeUtil.repositoryFriendlyDateFromEpoch(fromDate));
    return mlpGradeMetrics.stream()
        .map(
            m ->
                MlpMetricResponse.builder()
                    .name(m.getName())
                    .slug(m.getSlug())
                    .count(m.getMlpCount())
                    .attendancePercentage(m.getAttendancePercentage())
                    .build())
        .toList();
  }

  private List<MlpMetricResponse> mapMlpMetrics(@NonNull List<MlpGradeMetrics> mlpGradeMetrics) {
    return mlpGradeMetrics.stream()
        .map(m -> MlpMetricResponse.builder().name(m.getName()).count(m.getMlpCount()).build())
        .toList();
  }

  public List<MlpMetricResponse> getChildInstitutionGradeMetrics(Long fromDate) {
    User user = authService.getUserDetails();
    List<Organization> childOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());

    List<String> childOrgSlugs = childOrgs.stream().map(Organization::getSlug).toList();
    List<MlpGradeMetrics> mlpGradeMetrics =
        mlpRepository.getChildInstitutionGradeMetrics(
            childOrgSlugs, dateTimeUtil.repositoryFriendlyDateFromEpoch(fromDate));
    return mapMlpMetrics(mlpGradeMetrics);
  }

  public List<MlpMetricResponse> getChildInstitutionSubjectMetrics(Long fromDate) {
    User user = authService.getUserDetails();
    List<Organization> childOrgs = teacherOrgsService.getChildOrgs(user.getAuthUserId());

    List<String> childOrgSlugs = childOrgs.stream().map(Organization::getSlug).toList();

    List<MlpGradeMetrics> mlpGradeMetrics =
        mlpRepository.getChildInstitutionSubjectMetrics(
            childOrgSlugs, dateTimeUtil.repositoryFriendlyDateFromEpoch(fromDate));
    return mapMlpMetrics(mlpGradeMetrics);
  }

  public List<GenericMetricResponse> getMlpCountByWeek(List<String> orgSlugs, Integer noOfWeeks) {

    LocalDate toDate = LocalDate.now();
    LocalDate fromDate = LocalDate.now().minusWeeks(noOfWeeks);

    List<LocalDate> requiredDates = dateTimeUtil.getDates(fromDate.plusDays(1), toDate);
    List<Long> epochDates = new ArrayList<>();
    requiredDates.forEach(
        date -> epochDates.add(dateTimeUtil.getEpochFromStringDate(String.valueOf(date))));

    List<MlpAnalytics> mlpAnalytics = mlpRepository.fetchMlpWeeklyCount(orgSlugs, fromDate, toDate);

    List<GenericMetricResponse> mlpsCount =
        mlpAnalytics.stream()
            .map(
                m ->
                    GenericMetricResponse.builder()
                        .date(getEpochFromStringDate(m.getDate()))
                        .count(m.getMlpCount())
                        .studentMlpCount(m.getStudentMlpCount())
                        .build())
            .toList();

    LinkedHashMap<Long, Integer> teacherMlpsMap = new LinkedHashMap<>();
    LinkedHashMap<Long, Integer> studentMlpsMap = new LinkedHashMap<>();

    requiredDates.forEach(
        date -> teacherMlpsMap.put(getEpochFromStringDate(String.valueOf(date)), 0));
    requiredDates.forEach(
        date -> studentMlpsMap.put(getEpochFromStringDate(String.valueOf(date)), 0));

    mlpsCount.forEach(
        result -> {
          if (teacherMlpsMap.containsKey(result.getDate())) {
            teacherMlpsMap.put(result.getDate(), result.getCount());
            studentMlpsMap.put(result.getDate(), result.getStudentMlpCount());
          }
        });

    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    for (int key = 0; key < epochDates.size(); key = key + 7) {
      int teacherMlpsCount =
          teacherMlpsMap.get(epochDates.get(key))
              + teacherMlpsMap.get(epochDates.get(key + 1))
              + teacherMlpsMap.get(epochDates.get(key + 2))
              + teacherMlpsMap.get(epochDates.get(key + 3))
              + teacherMlpsMap.get(epochDates.get(key + 4))
              + teacherMlpsMap.get(epochDates.get(key + 5))
              + teacherMlpsMap.get(epochDates.get(key + 6));
      int studentMlpsCount =
          studentMlpsMap.get(epochDates.get(key))
              + studentMlpsMap.get(epochDates.get(key + 1))
              + studentMlpsMap.get(epochDates.get(key + 2))
              + studentMlpsMap.get(epochDates.get(key + 3))
              + studentMlpsMap.get(epochDates.get(key + 4))
              + studentMlpsMap.get(epochDates.get(key + 5))
              + studentMlpsMap.get(epochDates.get(key + 6));

      Long tempDate = epochDates.get(key + 6);
      GenericMetricResponse genericMetricResponse = new GenericMetricResponse();
      genericMetricResponse.setDate(tempDate);
      genericMetricResponse.setCount(teacherMlpsCount);
      genericMetricResponse.setStudentMlpCount(studentMlpsCount);
      genericMetricResponses.add(genericMetricResponse);
    }

    genericMetricResponses.sort(Comparator.comparing(GenericMetricResponse::getDate).reversed());
    return genericMetricResponses;
  }

  public List<GenericMetricResponse> getMlpsCountByDay(List<String> orgSlugs) {

    LocalDate today = LocalDate.now();
    List<MlpAnalytics> mlpAnalytics = mlpRepository.getMlpCountDateWise(orgSlugs);
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();
    genericMetricResponses.addAll(
        mlpAnalytics.stream()
            .map(
                m ->
                    GenericMetricResponse.builder()
                        .date(getEpochFromStringDate(m.getDate()))
                        .count(m.getMlpCount())
                        .studentMlpCount(m.getStudentMlpCount())
                        .build())
            .toList());

    GenericMetricResponse totalAssetsCount = new GenericMetricResponse();
    totalAssetsCount.setDate(DateTimeUtil.convertIso8601ToEpoch(today.atStartOfDay()));
    totalAssetsCount.setCount(mlpRepository.getMlpCount(orgSlugs));
    totalAssetsCount.setStudentMlpCount(mlpRepository.getStudentMlpAttemptedCount(orgSlugs));
    genericMetricResponses.addFirst(totalAssetsCount);
    return genericMetricResponses;
  }

  public List<GenericMetricResponse> getMlpsCountByMonth(
      List<String> orgSlugs, Integer noOfMonths) {
    LocalDate toDate = LocalDate.now();
    LocalDate fromDate = LocalDate.now().minusMonths(noOfMonths);

    List<Integer> requiredMonths = DateTimeUtil.getLast5Months();
    List<MlpAnalytics> mlpAnalytics = mlpRepository.getMlpCountByMonth(orgSlugs, fromDate, toDate);
    LinkedHashMap<String, Integer> teacherMlpsMap = new LinkedHashMap<>();
    LinkedHashMap<String, Integer> studentMlpsMap = new LinkedHashMap<>();

    requiredMonths.forEach(month -> teacherMlpsMap.put(String.valueOf(month), 0));
    requiredMonths.forEach(month -> studentMlpsMap.put(String.valueOf(month), 0));

    mlpAnalytics.forEach(
        result -> {
          if (teacherMlpsMap.containsKey(String.valueOf(result.getDate()))) {
            teacherMlpsMap.put(result.getDate(), result.getMlpCount());
            studentMlpsMap.put(result.getDate(), result.getStudentMlpCount());
          }
        });
    List<GenericMetricResponse> genericMetricResponses = new ArrayList<>();

    requiredMonths.forEach(
        month -> {
          LocalDate temp = LocalDate.of(DateTimeUtil.findYearByMonthNumber(month), month, 1);
          LocalDateTime l1 = temp.atStartOfDay();
          genericMetricResponses.add(
              GenericMetricResponse.builder()
                  .date(dateTimeUtil.convertIso8601ToEpoch(l1))
                  .count(teacherMlpsMap.get(String.valueOf(month)))
                  .studentMlpCount(studentMlpsMap.get(String.valueOf(month)))
                  .build());
        });
    return genericMetricResponses;
  }

  public void updateMlpInstIfExists(Exam exam) {

    Student student = exam.getStudent();

    if (exam.getRef() == null) {
      return;
    }
    Optional<Mlp> mlp = mlpRepository.getFirstByExamRef(exam.getRef());
    if (mlp.isEmpty()) {
      return;
    }
    Mlp selectedMlp = mlp.get();
    var getStudent =
        mlp.get().getMlpInsts().stream()
            .filter(s -> s.getStudent().getId() == student.getId())
            .findFirst();
    if (getStudent.isEmpty()) {
      addMlpInstForStudent(student, selectedMlp);
    }
    Optional<MlpInst> optionalMlpInst =
        mlpInstRepository.getMlpInstByMlpAndStudent(selectedMlp, student);

    if (optionalMlpInst.isPresent()) {
      var mlpInst = optionalMlpInst.get();
      if (mlpInst.getExam() == null) {
        mlpInst.setExam(exam);
        var studentPercentage =
            calculateStudentAttendanceAndKnowledgePercentage(selectedMlp, student.getId(), mlpInst);
        mlpInst.setKnowledgePercentage(studentPercentage.getKnowledgePercentage());
        mlpInst.setAttendancePercentage(studentPercentage.getAttendancePercentage());
        var totalPercentage = calculateTotalMlpPercentage(selectedMlp);
        selectedMlp.setAttendancePercentage(totalPercentage.getAttendancePercentage());
        selectedMlp.setKnowledgePercentage(totalPercentage.getKnowledgePercentage());
        mlpRepository.save(selectedMlp);
        mlpAttendanceService.updateMlpAttendance(selectedMlp, student);
      }
    }
  }

  public void addMlpInstForStudent(Student student, Mlp selectedMlp) {
    MlpInst studentRecord = new MlpInst();
    studentRecord.setStudent(student);
    studentRecord.setMlp(selectedMlp);
    studentRecord.setSynopsisStatus(MlpItemStatus.NOT_STARTED);
    studentRecord.setVideoStatus(MlpItemStatus.NOT_STARTED);
    mlpInstRepository.save(studentRecord);
  }

  private MlpKnowledgeAndAttendanceData calculateStudentAttendanceAndKnowledgePercentage(
      Mlp selectedMlp, Long studentId, MlpInst mlpInst) {
    MlpKnowledgeAndAttendanceData data = new MlpKnowledgeAndAttendanceData();
    var totalCount = getMlpAttributeCount(selectedMlp);
    data.setKnowledgePercentage(
        calculateStudentKnowledgePercentage(selectedMlp, studentId, selectedMlp.getExamRef()));
    data.setAttendancePercentage(
        calculateStudentAttendancePercentage(
            selectedMlp, mlpInst, studentId, selectedMlp.getExamRef(), totalCount));
    return data;
  }

  private MlpKnowledgeAndAttendanceData calculateTotalMlpPercentage(Mlp selectedMlp) {
    MlpKnowledgeAndAttendanceData data = new MlpKnowledgeAndAttendanceData();
    var mlpKnowledgeAndAttendanceData = getMlpKnowledgeAndAttendaceAverage(selectedMlp);
    data.setKnowledgePercentage(calculateMlpKnowledgePercentage(mlpKnowledgeAndAttendanceData));
    data.setAttendancePercentage(calculateMlpAttendancePercentage(mlpKnowledgeAndAttendanceData));
    return data;
  }

  public List<MlpLearningAnalyticsByStudent> getMlpDetailsByStudent(
      String orgSlug, String sectionUuid, List<String> subjects, long date) {

    LocalDate fromDate = dateTimeUtil.convertEpochToIso8601Legacy(date).toLocalDate();
    List<StudentMlpDetails> studentMlpDetailsList = new ArrayList<>();
    if (sectionUuid.equals(ALL_SECTIONS)) {
      List<SectionEntityDto.Response> allSectionUuids =
          sectionService.getAllSections(orgSlug, false);
      List<UUID> sectionUuidsList = allSectionUuids.stream().map(v -> v.uuid()).toList();
      studentMlpDetailsList.addAll(
          mlpRepository.getMlpStudentDetails(sectionUuidsList, subjects, fromDate));
    } else {
      studentMlpDetailsList.addAll(
          mlpRepository.getMlpStudentDetails(
              Collections.singletonList(UUID.fromString(sectionUuid)), subjects, fromDate));
    }
    if (studentMlpDetailsList.isEmpty()) {
      return Collections.emptyList();
    }

    return studentMlpDetailsList.stream()
        .map(
            m -> {
              List<SubjectProfileDetailsResponse> subjectProfiles =
                  subjectProfilesService.getSubjectProfileDetailsOfStudent(m.getStudentId());
              List<String> mappedSubjects =
                  subjectProfiles.stream().map(sp -> sp.getSubjectName()).toList();
              return MlpLearningAnalyticsByStudent.builder()
                  .fullName(m.getFullName())
                  .userName(m.getUserName())
                  .sectionName(m.getSectionName())
                  .subjects(mappedSubjects)
                  .totalMlpCount(m.getTotalMlpCount())
                  .attemptedMlpCount(m.getAttemptedMlpCount())
                  .attendancePercentage(
                      Double.parseDouble(format.format(m.getAttendancePercentage())))
                  .knowledgePercentage(
                      Double.parseDouble(format.format(m.getKnowledgePercentage())))
                  .build();
            })
        .toList();
  }

  public void migrateMlp() {
    var mlpList = mlpRepository.getAllMlpsByNullAm();
    for (var mlp : mlpList) {
      for (var mlpInst : mlp.getMlpInsts()) {
        var studentPercentage =
            calculateStudentAttendanceAndKnowledgePercentage(
                mlp, mlpInst.getStudent().getId(), mlpInst);
        mlpInst.setKnowledgePercentage(studentPercentage.getKnowledgePercentage());
      }
      var totalPercentage = calculateTotalMlpPercentage(mlp);
      mlp.setKnowledgePercentage(totalPercentage.getKnowledgePercentage());
      mlpRepository.save(mlp);
    }
  }

  public QuestionsAnalytics getMlpDetailById(long mlpId, String bearerToken, String childOrg) {
    Optional<Mlp> mlp = mlpRepository.findById(mlpId);
    if (mlp.isEmpty()) {
      log.error(MLP_NOT_FOUND);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MlpFind.MLPId");
    }
    List<Long> allMlps = getChildMlpsIfExist(mlpId);
    allMlps.add(mlpId);
    List<MlpDetails> mlpData = mlpRepository.getMlpDetails(allMlps, childOrg);
    var mlpDetails = mlpData.getFirst();
    if (mlpDetails.getQuestionCount() == 0) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.MLP.Questions",
          new String[] {mlpDetails.getMlpName()});
    }
    var attemptedStudentCount =
        mlpData.stream().mapToLong(MlpDetails::getMlpAttemptedStudentCount).sum();
    var totalStudentCount = mlpData.stream().mapToLong(MlpDetails::getTotalNoStudent).sum();
    var teacher = validateTeacher(mlp.get().getTeacher().getId());
    var teacherUser = teacher.getUserInfo();
    var teacherName = teacherUser.getFirstName() + " " + teacherUser.getLastName();
    QuestionResponse mlpQuestions =
        getMlpQuestions(mlpDetails.getExamRef(), bearerToken, true, true);
    List<Question> questions = mlpQuestions.getData();
    List<QuestionDetails> questionDetails = mlpRepository.getQuestionDetails(allMlps, childOrg);

    return QuestionsAnalytics.builder()
        .title(mlpDetails.getMlpName())
        .createdDate(
            DateTimeUtil.convertIso8601ToEpoch(mlpDetails.getCreatedDate().toLocalDateTime()))
        .attemptedStudentCount((int) attemptedStudentCount)
        .totalStudentsCount((int) totalStudentCount)
        .chapter(List.of(mlpDetails.getChapter()))
        .subject(mlpDetails.getSubject())
        .subTopic(Collections.singletonList(mlpDetails.getSubTopic()))
        .teacherName(teacherName)
        .questionDetails(questionDetails)
        .questionData(questions)
        .build();
  }

  public List<MlpStudentDetail> fetchStudentResponseOfMlpsByGradeAndSubject(
      String childOrg,
      String gradeSlug,
      String subjectSlug,
      int limit,
      long fromDate,
      long toDate) {

    User user = authService.getUserDetails();
    String fd = dateTimeUtil.repositoryFriendlyDateFromEpoch(fromDate);
    String td = dateTimeUtil.repositoryFriendlyDateFromEpoch(toDate);

    if (!subjectSlug.isEmpty()) {
      return buildMlpStudentDetail(
          mlpRepository.fetchMlpByOrgSlugAndGradeSlugAndSubjectSlugAndCreatedAtAfter(
              childOrg, gradeSlug, subjectSlug, fd, td, PageRequest.of(0, limit)));
    }

    if (!gradeSlug.isEmpty()) {
      return buildMlpStudentDetail(
          mlpRepository.fetchMlpByOrgSlugAndGradeSlug(childOrg, gradeSlug, fd, td));
    }

    if (!childOrg.isEmpty()) {
      return buildMlpStudentDetail(mlpRepository.fetchMlpByChildOrg(childOrg, fd, td));
    }

    List<String> childOrgs =
        teacherOrgsService.getChildOrgs(user.getAuthUserId()).stream()
            .map(Organization::getSlug)
            .toList();

    if (!childOrgs.isEmpty()) {
      List<Mlp> mlps = mlpRepository.getMlpsByOrgSlugs(childOrgs, fd, td, limit);
      return buildMlpStudentDetail(mlps);
    }

    return new ArrayList<>();
  }

  private List<MlpStudentDetail> buildMlpStudentDetail(List<Mlp> mlps) {

    List<MlpStudentDetail> studentDetails = new ArrayList<>();

    List<MlpInst> mlpInsts =
        mlpInstRepository.getAllMlpInst(mlps.stream().map(Mlp::getId).toList());

    mlpInsts.forEach(
        inst ->
            studentDetails.add(
                MlpStudentDetail.builder()
                    .userName(
                        Objects.nonNull(inst.getStudent().getUserInfo())
                            ? inst.getStudent().getUserInfo().getUserName()
                            : "")
                    .fullName(userService.getNameByUserInfo(inst.getStudent().getUserInfo()))
                    .mlpName(inst.getMlp().getTitle())
                    .chapter(inst.getMlp().getChapterName())
                    .subject(inst.getMlp().getSubjectName())
                    .practiceStatus(
                        Objects.isNull(inst.getExam()) ? NOT_STARTED : PRACTICE_COMPLETED)
                    .synopsisStatus(inst.getSynopsisStatus())
                    .videoStatus(inst.getVideoStatus())
                    .attendancePercentage(
                        Objects.isNull(inst.getAttendancePercentage())
                            ? 0F
                            : inst.getAttendancePercentage())
                    .percentage(
                        Objects.isNull(inst.getKnowledgePercentage())
                            ? 0F
                            : inst.getKnowledgePercentage().floatValue())
                    .subtopicSlug(inst.getMlp().getSubtopicSlug())
                    .synopsisSlug(inst.getMlp().getSynopsisSlug())
                    .videoSlug(inst.getMlp().getVideoSlug())
                    .orgSlug(
                        Objects.nonNull(inst.getStudent().getUserInfo())
                            ? inst.getStudent().getUserInfo().getOrganization()
                            : null)
                    .mlpId(inst.getMlp().getId())
                    .section(inst.getMlp().getSection().getName())
                    .build()));
    return studentDetails.stream()
        .sorted(Comparator.comparing(MlpStudentDetail::getMlpId).reversed())
        .toList();
  }

  public List<InActiveMlpUsers> getInActiveMlpUsers(
      String childOrg, int noOfDays, int limit, String teacherAuthUserId) {

    List<InActiveMlpUsers> inActiveMlpUsers = new ArrayList<>();

    if (!childOrg.isEmpty()) {
      inActiveMlpUsers.addAll(buildInActiveUsersListUsingOrg(childOrg, noOfDays, limit));
      return inActiveMlpUsers;
    }

    List<String> childOrgs =
        teacherOrgsService.getChildOrgs(teacherAuthUserId).stream()
            .map(Organization::getSlug)
            .toList();

    if (childOrgs.isEmpty()) {
      return new ArrayList<>();
    }

    childOrgs.forEach(
        orgSlug ->
            inActiveMlpUsers.addAll(buildInActiveUsersListUsingOrg(orgSlug, noOfDays, limit)));
    return inActiveMlpUsers.stream().limit(limit).toList();
  }

  private List<InActiveMlpUsers> buildInActiveUsersListUsingOrg(
      String childOrg, int noOfDays, int limit) {

    Organization organization = organizationRepository.findBySlug(childOrg);
    OrganizationStrength organizationStrength = userRepository.getOrganizationStrength(childOrg);
    User principal = userRepository.fetchPrincipalOfOrganization(childOrg);

    LocalDate toDate = LocalDate.now();
    LocalDate fromDate = toDate.minusDays(noOfDays);
    List<LocalDate> requiredDates = dateTimeUtil.getDates(fromDate, toDate);
    List<InActiveMlpUsers> inActiveMlpUsers = new ArrayList<>();

    for (var requiredDate : requiredDates) {
      List<BasicUserInfo> teacherQueryResult =
          mlpRepository.getMlpsNotAssignedTeachers(childOrg, requiredDate).stream()
              .limit(limit)
              .toList();

      List<BasicUserInfo> studentQueryResult =
          mlpRepository.getMlpsNotAttendedStudents(childOrg, requiredDate).stream()
              .limit(limit)
              .toList();

      List<UserDetails> mlpNotAssignedTeachers = new ArrayList<>();
      teacherQueryResult.forEach(
          r ->
              mlpNotAssignedTeachers.add(
                  UserDetails.builder().fullName(r.getFullName()).email(r.getEmail()).build()));

      List<UserDetails> mlpNotAttendedStudents = new ArrayList<>();
      studentQueryResult.forEach(
          r ->
              mlpNotAttendedStudents.add(
                  UserDetails.builder().fullName(r.getFullName()).section(r.getSection()).build()));

      inActiveMlpUsers.add(
          InActiveMlpUsers.builder()
              .principalName(
                  principal.getFirstName()
                      + " "
                      + principal.getLastName()
                      + "\n"
                      + principal.getMobileNumber())
              .date(requiredDate.getMonth().toString() + " " + requiredDate.getDayOfMonth())
              .orgName(organization.getName())
              .mlpsAttendedStudents(
                  organizationStrength.getStudentsStrength() - studentQueryResult.size())
              .totalStudents(organizationStrength.getStudentsStrength())
              .mlpsAssignedTeachers(
                  organizationStrength.getTeachersStrength() - teacherQueryResult.size())
              .totalTeachers(organizationStrength.getTeachersStrength())
              .mlpsNotAttendedStudents(mlpNotAttendedStudents)
              .mlpsNotAssignedTeachers(mlpNotAssignedTeachers)
              .build());
    }

    return inActiveMlpUsers;
  }

  public List<MlpResponse> getAllMlpsByOrgAndTeacher(
      String childOrg,
      String teacherUuid,
      int limit,
      String parentOrg,
      long fromDate,
      long toDate,
      List<String> subjectSlug) {

    String fd = dateTimeUtil.repositoryFriendlyDateFromEpoch(fromDate);
    String td = dateTimeUtil.repositoryFriendlyDateFromEpoch(toDate);
    User user = authService.getUserDetails();

    if (!userRoleHelper.isManager(user)) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.MLPResponseNotAuthorized");
    }
    List<String> childOrgs =
        teacherOrgsService.getChildOrgs(user.getAuthUserId()).stream()
            .map(Organization::getSlug)
            .toList();
    if (!childOrg.isEmpty() && teacherUuid.isEmpty()) {
      if (!childOrgs.contains(childOrg)) {
        throw new ApiException(
            InternalErrorCodes.UN_AUTHORIZED,
            "error.OrganizationChild.Parent",
            new String[] {childOrg, parentOrg});
      }
      return getMlpResponses(
          mlpRepository.getMlpsByOrgSlugsFromDateToDate(childOrgs, fd, td, limit, subjectSlug));
    }
    List<Mlp> mlps;

    List<OrgTeacher> orgTeachers = teacherRepository.findAllTeachers(childOrg);
    if (!teacherUuid.isEmpty()) {
      if (!orgTeachers.stream().map(OrgTeacher::getAuthUserId).toList().contains(teacherUuid)) {
        throw new ApiException(
            InternalErrorCodes.UN_AUTHORIZED,
            "error.TeacherUuid.ChildOrg",
            new String[] {teacherUuid, childOrg});
      }
      mlps =
          mlpRepository.getMlpsByTeacherUuidAndDateBetween(teacherUuid, fd, td, limit, subjectSlug);
      return getMlpResponses(mlps);
    }

    mlps = mlpRepository.getMlpsByOrgSlugsFromDateToDate(childOrgs, fd, td, limit, subjectSlug);
    return getMlpResponses(mlps);
  }

  public List<MlpsQuestionsOptionsData> getMlpsQuestionsAnalytics(long mlpId) {
    var queryResult = examRepository.getMlpsExamAnswersByMlpId(mlpId);

    List<MlpsQuestionsOptionsData> questionsAnalysisResponse = new ArrayList<>();

    queryResult.forEach(
        question -> {
          int attemptedStudents =
              question.getOptionOneCount()
                  + question.getOptionTwoCount()
                  + question.getOptionThreeCount()
                  + question.getOptionFourCount();

          questionsAnalysisResponse.add(
              MlpsQuestionsOptionsData.builder()
                  .optionOne(
                      (Double.parseDouble(
                          decimalFormat.format(
                              (question.getOptionOneCount() * 100.0) / attemptedStudents))))
                  .optionTwo(
                      (Double.parseDouble(
                          decimalFormat.format(
                              (question.getOptionTwoCount() * 100.0) / attemptedStudents))))
                  .optionThree(
                      (Double.parseDouble(
                          decimalFormat.format(
                              (question.getOptionThreeCount() * 100.0) / attemptedStudents))))
                  .optionFour(
                      (Double.parseDouble(
                          decimalFormat.format(
                              (question.getOptionFourCount() * 100.0) / attemptedStudents))))
                  .noOfStudentsAttempted(attemptedStudents)
                  .mlpId(question.getMlpId())
                  .questionUuid(question.getQuestionUuid())
                  .optionOneCount(question.getOptionOneCount())
                  .optionTwoCount(question.getOptionTwoCount())
                  .optionThreeCount(question.getOptionThreeCount())
                  .optionFourCount(question.getOptionFourCount())
                  .build());
        });
    return questionsAnalysisResponse;
  }

  public StudentsDetailsByMlpDto.StudentsDetailsByMlp getStudentsDetailsandMlpDetails(
      String orgSlug, String studentAuthId) {

    var studentMlpDetails = mlpInstRepository.getStudentDetailByMlp(orgSlug, studentAuthId);
    var mlpDetails =
        studentMlpDetails.stream()
            .map(
                detail ->
                    StudentsDetailsByMlpDto.MlpDetails.builder()
                        .mlpName(detail.getMlpName())
                        .mlpId(detail.getmlpId())
                        .date(getDate(detail.getDate()))
                        .attendancePercentage(detail.getAttendancePercentage())
                        .knowledgePercentage(detail.getKnowledgePercentage())
                        .videoStatus(detail.getVideoStatus())
                        .examId(detail.getExamId())
                        .practiceStatus(detail.getPracticeStatus())
                        .synopsisStatus(detail.getSynopsisStatus())
                        .build())
            .toList();

    return StudentsDetailsByMlpDto.StudentsDetailsByMlp.builder()
        .userName(studentMlpDetails.getFirst().getUserName())
        .fullName(studentMlpDetails.getFirst().getFullName())
        .boardId(studentMlpDetails.getFirst().getBoardId())
        .gradeName(studentMlpDetails.getFirst().getGradeName())
        .sectionName(studentMlpDetails.getFirst().getSectionName())
        .mlpDetails(mlpDetails)
        .build();
  }

  private Long getDate(Timestamp date) {
    if (date == null) {
      return null;
    } else {
      return DateTimeUtil.convertIso8601ToEpoch(date.toLocalDateTime());
    }
  }

  public List<GroupMlpDto.GroupMlpResponse> groupMlpTest(
      GroupMlpDto.GroupMlpRequest groupMlpRequest) {
    if (switchOffGroupMlp) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.scheduleTest.UnAuthorized",
          new String[] {"GroupMlp"});
    }
    Integer grade = contentService.getGradeBySlug(groupMlpRequest.gradeSlug()).getId();
    Set<Section> sectionForGrade;
    List<Mlp> allGroupMlps = new ArrayList<>();
    sectionForGrade = sectionRepository.getSectionsByOrganizationAndGradeId(WEXL_INTERNAL, grade);
    var teacherId = authService.getTeacherDetails().getTeacherInfo().getId();
    Optional<Teacher> teacherForOrg = teacherRepository.findById(teacherId);
    if (sectionForGrade.size() > 1) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MulitpleSections");
    }
    if (teacherForOrg.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Teacher/AdminNotFound");
    }
    AdvancedMlpRequest mlpRequest = groupMlpRequestToAdvancedMlpRequest(groupMlpRequest);
    mlpRequest.setTeacherId(teacherId);

    final Mlp parent =
        mlpRepository.save(
            createMlpSectionWise(
                teacherForOrg.get(),
                sectionForGrade.stream().toList().getFirst(),
                mlpRequest,
                WEXL_INTERNAL));
    parent.setQuestionUuids(groupMlpRequest.questionUuids());
    parent.setQuestionsAssigneeMode(QuestionsAssigneeMode.MANUAL);
    allGroupMlps.add(parent);
    for (String org : groupMlpRequest.organizationSlug()) {
      teacherForOrg =
          teacherRepository.findById(teacherRepository.getAllOrgAdminTeachers(org).getTeacherId());
      sectionForGrade = sectionRepository.getSectionsByOrganizationAndGradeId(org, grade);
      Optional<Teacher> finalTeacherForOrg = teacherForOrg;
      List<Mlp> mlps =
          sectionForGrade.stream()
              .map(
                  eachSection -> {
                    Mlp mlp =
                        createMlpSectionWise(
                            finalTeacherForOrg.get(), eachSection, mlpRequest, org);
                    mlp.setParent(parent);
                    mlp.setQuestionsAssigneeMode(QuestionsAssigneeMode.MANUAL);
                    mlp.setQuestionUuids(mlpRequest.getQuestionUuids());
                    return mlp;
                  })
              .toList();
      mlpRepository.saveAll(mlps);
      allGroupMlps.addAll(mlps);
    }

    return allGroupMlps.stream()
        .map(
            mlp ->
                GroupMlpDto.GroupMlpResponse.builder()
                    .mlpId(mlp.getId())
                    .startDate(mlp.getStartDate())
                    .build())
        .toList();
  }

  private AdvancedMlpRequest groupMlpRequestToAdvancedMlpRequest(
      GroupMlpDto.GroupMlpRequest groupMlpRequest) {
    AdvancedMlpRequest mlpRequest = new AdvancedMlpRequest();
    mlpRequest.setTitle(groupMlpRequest.title());
    mlpRequest.setSubjectSlug(groupMlpRequest.subjectSlug());
    mlpRequest.setChapterSlug(groupMlpRequest.chapterSlug());
    mlpRequest.setSubtopicSlug(groupMlpRequest.subtopicSlug());
    mlpRequest.setVideoSlug(groupMlpRequest.videoSlug());
    mlpRequest.setShaLink(groupMlpRequest.shaLink());
    mlpRequest.setQuestionUuids(groupMlpRequest.questionUuids());
    mlpRequest.setBoardSlug(groupMlpRequest.boardSlug());
    mlpRequest.setGradeSlug(groupMlpRequest.gradeSlug());
    mlpRequest.setVideoSource(groupMlpRequest.videoSource());
    mlpRequest.setSynopsisSlug(groupMlpRequest.synopsisSlug());
    mlpRequest.setDescription(groupMlpRequest.description());
    mlpRequest.setQuestionCount(groupMlpRequest.questionCount());
    return mlpRequest;
  }

  @Transactional
  public void submitMlpExam(
      BulkMlpRequest bulkMlpRequest, String orgSlug, String examRef, String bearerToken) {
    try {
      bulkMlpRequest
          .data()
          .forEach(
              data -> {
                Student student =
                    studentRepository.getStudentByAuthUserIdAndOrgSlug(data.authUserId(), orgSlug);
                ExamResponse examResponse = startMlpExam(student, orgSlug, examRef, bearerToken);
                data.questions()
                    .forEach(
                        questions -> {
                          StudentAnswerPracticeRequest studentAnswerPracticeRequest =
                              new StudentAnswerPracticeRequest();
                          Question questionByUuid =
                              contentService.getQuestionByUuid(
                                  bearerToken, QuestionType.MCQ, questions.questionUuid());
                          studentAnswerPracticeRequest.setQuestionUuid(questions.questionUuid());
                          studentAnswerPracticeRequest.setQuestionId(questionByUuid.getId());
                          studentAnswerPracticeRequest.setSelectedAnswer(
                              questions.selectedAnswer());
                          studentAnswerPracticeRequest.setExamId(examResponse.getId());
                          studentAnswerService.submitPracticeAnswer(
                              studentAnswerPracticeRequest, bearerToken);
                        });
                examService.completeExam(examResponse.getId());
              });
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public MlpAssetHandler getAssets() {
    if (mlpAssetHandlers.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "error.CannotFindConfiguration",
          new String[] {"assetsHandlers"});
    }
    return mlpAssetHandlers.getFirst();
  }

  public MlpDto.MlpAssetResponse buildAssetsResponse(
      String orgSlug, String teacherId, String chapterSlug) {
    return MlpDto.MlpAssetResponse.builder()
        .assetResponse(getAssets().getAssets(orgSlug, teacherId, chapterSlug))
        .build();
  }
}
