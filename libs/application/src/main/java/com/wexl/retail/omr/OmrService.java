package com.wexl.retail.omr;

import static org.apache.commons.lang3.math.NumberUtils.toFloat;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.mlp.service.omr.OmrResultFileProcessor;
import com.wexl.retail.model.Student;
import com.wexl.retail.omr.OmrDto.OmrTaskDetailResponse;
import com.wexl.retail.omr.OmrDto.QuestionResult;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.schedule.dto.StudentTestAttemptStatus;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.repository.StudentScheduleTestAnswerRepository;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.dto.QuestionDto.Question;
import com.wexl.retail.test.school.dto.QuestionDto.TestDefinitionSectionResponse;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.thymeleaf.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class OmrService {

  private final ValidationUtils validationUtils;
  private final StorageService storageService;
  private final OmrResultFileProcessor omrResultFileProcessor;
  private final StudentScheduleTestAnswerRepository studentScheduleTestAnswerRepository;
  private final StudentRepository studentRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final TestDefinitionService testDefinitionService;
  private final ScheduleTestRepository scheduleTestRepository;
  private final RestTemplate restTemplate;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private static final String OMR_FILE_EXTENSION = "zip";

  private static final String FILE_EXTENSION = "pdf";
  private static final String OMR_TEMPLATES_DIR = "omr-templates";

  @Value("${app.omr-url}")
  private String apiUrl;

  @Value("${spring.profiles.active:default}")
  private String envProfile;

  public void uploadTestScheduleOmr(OmrDto.TestScheduleRequest request, String orgSlug) {
    var testSchedule = validationUtils.isTestScheduleValid(request.testScheduleId());
    Long questionCount =
        testSchedule.getTestDefinition().getTestDefinitionSections().stream()
            .mapToLong(TestDefinitionSection::getNoOfQuestions)
            .sum();

    var transformCsv = transformCsv(orgSlug, request, questionCount);
    processCsv(transformCsv, testSchedule, orgSlug);
  }

  private void processCsv(
      List<OmrDto.TestScheduleResponse> transformCsv, ScheduleTest testSchedule, String orgSlug) {
    transformCsv.forEach(
        row -> {
          var optionalStudent = studentRepository.getStudentByRollNumber(row.rollNumber(), orgSlug);
          var student = validationUtils.isStudentValid(optionalStudent.get().getId());
          var tss = getTss(testSchedule, student.getUserInfo().getId());
          var updatedTssa = updateTssa(tss, row);
          studentScheduleTestAnswerRepository.saveAll(updatedTssa);
          tss.setStartTime(LocalDateTime.now().minusMinutes(testSchedule.getDuration()));
          tss.setEndTime(LocalDateTime.now());
          tss.setStatus(TestStudentStatus.SUBMITTED.name());
          scheduleTestStudentRepository.save(tss);
        });
  }

  public ScheduleTestStudent getTss(ScheduleTest testSchedule, Long userId) {
    var tss =
        testSchedule.getScheduleTestStudent().stream()
            .filter(
                x ->
                    x.getStudent().getId() == userId
                        && x.getScheduleTest().getId() == testSchedule.getId())
            .findFirst();
    if (tss.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    return tss.get();
  }

  private List<TestScheduleStudentAnswer> updateTssa(
      ScheduleTestStudent tss, OmrDto.TestScheduleResponse row) {
    List<QuestionDto.Question> questions =
        getMockTestQuestionsByTestDefinitionId(
            tss.getScheduleTest().getTestDefinition().getId(), 1);
    tss.setQuestionResponseSet(0);
    var tssa = studentScheduleTestAnswerRepository.findAllByTssUuid(tss.getUuid());

    for (int i = 1; i <= questions.size(); i++) {
      int questionCount = i;
      tssa.forEach(
          entry -> {
            if (QuestionType.MCQ.equals(entry.getQuestionType())
                && questions.get(questionCount - 1).uuid().equals(entry.getQuestionUuid())) {
              if (row.selectedQuestions().get(questionCount).contains("x")) {
                entry.setAttemptStatus(StudentTestAttemptStatus.NOT_MARKED);
              } else {
                entry.setAttemptStatus(StudentTestAttemptStatus.ANSWERED);
                entry.setMcqSelectedAnswer(
                    (OmrAnswers.valueOf(row.selectedQuestions().get(questionCount)).integer()));
              }
            }
          });
    }
    return tssa;
  }

  private List<OmrDto.TestScheduleResponse> transformCsv(
      String orgSlug, OmrDto.TestScheduleRequest request, double questionCount) {

    String filePath = getFilePath(orgSlug, request.fileReference(), request.extension());
    var fileInputStream = storageService.getInputStream(filePath);
    return omrResultFileProcessor.processTestScheduleOmr(fileInputStream, questionCount);
  }

  public String getFilePath(String orgSlug, String reference, String extension) {
    return "%s/omr/%s.%s".formatted(orgSlug, reference, extension);
  }

  @Transactional
  public void processOmr(OmrDto.OmrProcessResult omrProcessResult) {
    var scheduleTest = validationUtils.isTestScheduleValid(omrProcessResult.testScheduleId());
    var student =
        getStudentByRollNumber(
            omrProcessResult.studentRollNumber(), scheduleTest.getOrgSlug(), true);
    if (student == null) {
      return;
    }
    ScheduleTestStudent tss = getTss(scheduleTest, student.getUserInfo().getId());
    if (TestStudentStatus.SUBMITTED.name().equals(tss.getStatus())
        || TestStudentStatus.COMPLETED.name().equals(tss.getStatus())) {
      log.info(
          String.format(
              "Ignoring the omr process  for which test schedule student  already  %s",
              tss.getStatus()));
      return;
    }
    studentScheduleTestAnswerRepository.saveAll(updateStudentAnswers(tss, omrProcessResult));
    tss.setQuestionResponseSet(0);
    tss.setResultProcessingTime(LocalDateTime.now());
    tss.setStartTime(LocalDateTime.now().minusMinutes(tss.getScheduleTest().getDuration()));
    tss.setEndTime(LocalDateTime.now());
    tss.setStatus(TestStudentStatus.SUBMITTED.name());
    tss.setOmrImagePath(omrProcessResult.omrImagePath());
    scheduleTestStudentRepository.save(tss);
    if (Boolean.FALSE.equals(scheduleTest.getIsDac())) {
      scheduleTest.setIsDac(Boolean.TRUE);
      scheduleTestRepository.save(scheduleTest);
    }
  }

  private List<TestScheduleStudentAnswer> updateStudentAnswers(
      ScheduleTestStudent tss, OmrDto.OmrProcessResult omrProcessResult) {
    List<TestScheduleStudentAnswer> testScheduleStudentAnswers =
        studentScheduleTestAnswerRepository.findAllByTssUuid(tss.getUuid());

    List<QuestionDto.Question> questions =
        getMockTestQuestionsByTestDefinitionId(
            tss.getScheduleTest().getTestDefinition().getId(),
            validateSetNumber(omrProcessResult.setNumber()));

    var results = omrProcessResult.result();
    Map<String, OmrDto.QuestionResult> mapResult =
        results.stream()
            .collect(Collectors.toMap(OmrDto.QuestionResult::questionNumber, result -> result));

    for (int i = 1; i <= questions.size(); i++) {
      int questionNumber = i;
      testScheduleStudentAnswers.forEach(
          tssa -> {
            var questionResult = mapResult.get(String.valueOf(questionNumber));
            handleQuestions(
                tssa, questionResult, questions.get(questionNumber - 1), questionNumber);
          });
    }
    return testScheduleStudentAnswers;
  }

  private Integer validateSetNumber(Integer setNo) {
    if (setNo == null || setNo < 1 || setNo > 6) {
      return 1;
    }
    return setNo;
  }

  private void handleQuestions(
      TestScheduleStudentAnswer tssa,
      QuestionResult questionResult,
      Question question,
      int questionNumber) {
    if (!questionResult.questionNumber().equals(String.valueOf(questionNumber))) {
      return;
    }

    if (!question.uuid().equals(tssa.getQuestionUuid())) {
      return;
    }

    if (StringUtils.isEmpty(questionResult.questionAnswer())) {
      tssa.setAttemptStatus(StudentTestAttemptStatus.NOT_MARKED);
      return;
    }

    tssa.setAttemptStatus(StudentTestAttemptStatus.ANSWERED);
    if (QuestionType.MCQ.name().equals(questionResult.questionType())) {
      tssa.setMcqSelectedAnswer(
          OmrAnswers.valueOf(questionResult.questionAnswer().toLowerCase()).integer());
    } else if (QuestionType.NAT.name().equals(questionResult.questionType())) {
      tssa.setNatSelectedAnswer(toFloat(questionResult.questionAnswer()));
    }
  }

  private List<QuestionDto.Question> getMockTestQuestionsByTestDefinitionId(
      long testDefinitionId, Integer setNumber) {
    QuestionDto.QuestionResponse questionResponse =
        testDefinitionService.getTestDefinitionQuestions(testDefinitionId, setNumber);

    List<QuestionDto.Question> allQuestions = new ArrayList<>();
    questionResponse
        .testDefinitionSectionResponses()
        .sort(Comparator.comparing(TestDefinitionSectionResponse::seqNo));
    for (TestDefinitionSectionResponse response :
        questionResponse.testDefinitionSectionResponses()) {
      allQuestions.addAll(response.questions());
    }

    return allQuestions;
  }

  public OmrDto.OmrRetailTaskResponse getOmrTaskResponse(String orgSlug, Long testScheduleId) {
    var url = "%somr-tasks".formatted(apiUrl);
    var uriComponent =
        UriComponentsBuilder.fromUriString(url)
            .queryParam("namespace", envProfile)
            .queryParam("test_schedule_id", testScheduleId)
            .build()
            .toUri();
    ResponseEntity<OmrDto.OmrTaskResponse> responseEntity =
        restTemplate.exchange(uriComponent, HttpMethod.GET, null, OmrDto.OmrTaskResponse.class);
    var response = responseEntity.getBody();
    if (!responseEntity.getStatusCode().is2xxSuccessful() || Objects.isNull(response)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, responseEntity.getStatusCode().toString());
    }
    return OmrDto.OmrRetailTaskResponse.builder()
        .createdAt(response.createdAt())
        .status(response.status())
        .failureReason(response.failureReason())
        .omrTaskDetails(buildOmrTaskDetail(orgSlug, response.omrTaskDetailResponse()))
        .build();
  }

  private List<OmrDto.OmrRetailTaskDetailResponse> buildOmrTaskDetail(
      String orgSlug, List<OmrTaskDetailResponse> omrTaskDetailResponse) {
    return omrTaskDetailResponse.stream()
        .map(
            response -> {
              var student =
                  studentRepository.getStudentsByRollNumberAndOrg(
                      response.studentRollNumber(), orgSlug);
              return OmrDto.OmrRetailTaskDetailResponse.builder()
                  .result(response.result())
                  .failureReason(buildFailureReason(response))
                  .lastUpdatedAt(response.lastUpdatedAt())
                  .studentUserName(
                      !student.isEmpty() ? student.getFirst().getUserInfo().getUserName() : null)
                  .imageUrl(getOmrPreSignedUrl(response.imagePath()))
                  .rollNumber(response.studentRollNumber())
                  .studentName(
                      !student.isEmpty()
                          ? userService.getNameByUserInfo(student.getFirst().getUserInfo())
                          : null)
                  .status(response.status())
                  .build();
            })
        .toList();
  }

  private String buildFailureReason(OmrTaskDetailResponse response) {
    var isFailed = response.status().equals("FAILED");
    if (isFailed && response.failureReason().contains("Invalid format")) {
      return ("Invalid file format");
    }
    if (response.failureReason() == null) {
      return dateTimeUtil.buildDate(response.lastUpdatedAt());
    }
    return (dateTimeUtil.buildDate(response.lastUpdatedAt()) + " - " + response.failureReason());
  }

  private String getOmrPreSignedUrl(String path) {
    return Objects.nonNull(path)
        ? storageService.generatePreSignedUrlForFetch(path, "wexl-omr")
        : null;
  }

  public Student getStudentByRollNumber(
      String studentRollNumber, String orgSlug, boolean throwException) {
    List<Student> students;
    if (orgSlug.equals("ste143921")) {
      students = studentRepository.getStudentsByClassRollNumberAndOrg(studentRollNumber, orgSlug);
    } else {
      students = studentRepository.getStudentsByRollNumberAndOrg(studentRollNumber, orgSlug);
    }
    if (students.isEmpty() && throwException) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentNotFoundWithRollNumber",
          new String[] {studentRollNumber});
    } else if (students.size() > 1 && throwException) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ManyStudentsFound",
          new String[] {studentRollNumber});
    }
    if (students.size() == 1) {
      return students.getFirst();
    }

    return null;
  }

  public OmrDto.OmrTemplateDownloadResponse downloadOmrTemplate(
      String orgSlug, TestCategory fileType) {
    var filePath = buildTemplatePath(orgSlug, String.valueOf(fileType));
    var defaultFilePath = buildTemplatePath("wexl-internal", String.valueOf(fileType));
    if (storageService.isFileAvailable(filePath)) {
      return buildResponse(fileType, filePath);
    }
    return buildResponse(fileType, defaultFilePath);
  }

  private OmrDto.OmrTemplateDownloadResponse buildResponse(TestCategory fileType, String filePath) {
    return OmrDto.OmrTemplateDownloadResponse.builder()
        .fileType(fileType)
        .url(storageService.generatePreSignedUrlForFetch(filePath))
        .build();
  }

  private String buildTemplatePath(String orgSlug, String fileType) {
    return "%s/%s/%s.%s".formatted(orgSlug, OMR_TEMPLATES_DIR, fileType, FILE_EXTENSION);
  }

  public void submitOmr(OmrDto.OmrProcessingResponse omrProcessingResponse, String orgSlug) {
    OmrDto.OmrProcessingResponse omrProcessingRequest =
        OmrDto.OmrProcessingResponse.builder()
            .testScheduleId(omrProcessingResponse.testScheduleId())
            .formName(omrProcessingResponse.formName())
            .bucketName("wexl-omr")
            .filePath(buildFilePath(omrProcessingResponse, orgSlug))
            .ns(envProfile)
            .build();
    var url = "%somr-tasks".formatted(apiUrl);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_TYPE, "application/json");

    final HttpEntity<OmrDto.OmrProcessingResponse> requestEntity =
        new HttpEntity<>(omrProcessingRequest, headers);
    ResponseEntity<Void> responseEntity =
        restTemplate.exchange(url, HttpMethod.POST, requestEntity, Void.class);
    if (!responseEntity.getStatusCode().is2xxSuccessful()) {
      log.info("Unable to process the omr task");
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "Unable to process the omr task");
    }
  }

  private String buildFilePath(OmrDto.OmrProcessingResponse omrProcessingResponse, String orgSlug) {
    return "zips/%s/%s.%s".formatted(orgSlug, omrProcessingResponse.filePath(), OMR_FILE_EXTENSION);
  }
}
