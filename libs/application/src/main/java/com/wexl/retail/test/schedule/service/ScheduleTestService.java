package com.wexl.retail.test.schedule.service;

import static com.wexl.retail.content.model.QuestionType.SPCH;
import static com.wexl.retail.util.Constants.*;
import static java.util.Comparator.comparing;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.elp.service.SpeechEvaluationService;
import com.wexl.retail.model.GenericResponse;
import com.wexl.retail.model.User;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.qpgen.repository.QpGenRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.answer.StudentAnswerRepository;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.student.exam.competitive.processor.CompetitiveExamValidatorProcessor;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import com.wexl.retail.test.schedule.dto.*;
import com.wexl.retail.test.schedule.dto.ScheduleTestDto.GroupScheduleTestRequestBulk;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentResponseImpl;
import com.wexl.retail.test.schedule.repository.StudentScheduleTestAnswerRepository;
import com.wexl.retail.test.school.domain.*;
import com.wexl.retail.test.school.dto.TestDefinitionsDto;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.repository.TestQuestionRepository;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.NotificationUtils;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduleTestService {

  public static final String ACTIVE = "active";
  public static final String INPROGRESS = "inprogress";
  public static final String EXPIRED = "expired";
  public static final String UPCOMING = "upcoming";

  private final UserService userService;
  private final AuthService authService;
  private final DateTimeUtil dateTimeUtil;
  private final StrapiService strapiService;
  private final UserRepository userRepository;
  private final ExamRepository examRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ScheduleTestTransformer scheduleTestTransformer;
  private final StudentAnswerRepository studentAnswerRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final SectionRepository sectionRepository;

  private final StudentScheduleTestAnswerRepository studentScheduleTestAnswerRepository;
  private final CompetitiveExamValidatorProcessor competitiveExamValidatorProcessor;
  private final NotificationUtils notificationUtils;
  private final ContentService contentService;

  private static SecureRandom rand = new SecureRandom();
  private final QpGenRepository qpGenRepository;
  private final TestQuestionRepository testQuestionRepository;
  private final SpeechEvaluationService speechEvaluationService;
  private final TeacherRepository teacherRepository;
  private final ValidationUtils validationUtils;

  @Value("${app.switchOffGroupTest}")
  private boolean switchOffGroupTest;

  @Transactional
  public List<SimpleScheduleTestResponse> groupScheduleTestByGradeList(
      GroupScheduleTestRequestBulk groupScheduleTestRequestBulk, String parentOrg) {
    if (groupScheduleTestRequestBulk.grades() == null
        || groupScheduleTestRequestBulk.grades().isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.scheduleTest.NoGrades",
          new String[] {"GroupTest"});
    }

    final List<SimpleScheduleTestResponse> finalResponse = new ArrayList<>();
    groupScheduleTestRequestBulk
        .grades()
        .forEach(
            grade -> {
              GroupScheduleTestRequest groupScheduleTestRequest1 =
                  cloneWithGrade(groupScheduleTestRequestBulk, grade);
              finalResponse.addAll(groupScheduleTest(groupScheduleTestRequest1, parentOrg));
            });

    return finalResponse;
  }

  private GroupScheduleTestRequest cloneWithGrade(
      GroupScheduleTestRequestBulk groupScheduleTestRequest, String grade) {
    return GroupScheduleTestRequest.builder()
        .childOrgs(groupScheduleTestRequest.childOrgs())
        .duration(groupScheduleTestRequest.duration())
        .endDate(groupScheduleTestRequest.endDate())
        .startDate(groupScheduleTestRequest.startDate())
        .testDefinitionId(groupScheduleTestRequest.testDefinitionId())
        .boards(groupScheduleTestRequest.board())
        .grade(grade)
        .sectionIds(null)
        .studentIds(null)
        .build();
  }

  @Transactional
  public List<SimpleScheduleTestResponse> groupScheduleTest(
      GroupScheduleTestRequest groupScheduleTestRequest, String parentOrg) {
    if (parentOrg.equals(Constants.WEXL_INTERNAL) && switchOffGroupTest) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.scheduleTest.UnAuthorized",
          new String[] {"GroupTest"});
    }
    final ScheduleTest parent =
        scheduleTestRepository.save(constructScheduleTest(groupScheduleTestRequest, parentOrg));
    List<ScheduleTest> scheduleTests =
        groupScheduleTestRequest.getChildOrgs().stream()
            .map(
                org -> {
                  ScheduleTest scheduleTest = constructScheduleTest(groupScheduleTestRequest, org);
                  scheduleTest.setParent(parent);
                  return scheduleTest;
                })
            .toList();
    List<ScheduleTest> scheduleTestsUpdated = scheduleTestRepository.saveAll(scheduleTests);
    scheduleTestsUpdated.add(parent);
    sendNotification(scheduleTestsUpdated, parent.getTestDefinition().getType());
    var testDefinition =
        testDefinitionRepository.findById(groupScheduleTestRequest.getTestDefinitionId());
    if (testDefinition.isPresent() && testDefinition.get().getType().equals(TestType.MOCK_TEST)) {
      scheduleTestsUpdated.forEach(this::saveStudentScheduleTestAnswers);
    }

    return scheduleTestsUpdated.stream()
        .map(scheduleTestTransformer::mapTestScheduleToScheduleTestResponse)
        .toList();
  }

  private void sendNotification(List<ScheduleTest> scheduleTests, TestType type) {
    scheduleTests.forEach(
        scheduleTest -> {
          var studentList =
              scheduleTest.getScheduleTestStudent().stream()
                  .map(ScheduleTestStudent::getStudent)
                  .toList()
                  .stream()
                  .map(User::getStudentInfo)
                  .toList();
          var subjectName =
              strapiService.getSubjectNameBySlug(scheduleTest.getTestDefinition().getSubjectSlug());
          String testNameAndSubject =
              (subjectName.isBlank()
                      ? wrapQuotes(scheduleTest.getTestDefinition().getTestName())
                      : wrapQuotes(scheduleTest.getTestDefinition().getTestName())
                          + " in "
                          + subjectName)
                  .formatted();
          var message = NOTIFICATION_MESSAGE.formatted(type + testNameAndSubject);
          var attributes =
              NotificationDto.TestAttributes.builder()
                  .testId(scheduleTest.getId())
                  .testName(scheduleTest.getTestDefinition().getTestName())
                  .testType(type.name())
                  .subject(subjectName)
                  .build();
          notificationUtils.sendNotification(
              message, studentList, scheduleTest.getOrgSlug(), attributes);
        });
  }

  public static String wrapQuotes(String inputString) {
    return "\"" + inputString + "\"";
  }

  public ScheduleTest constructScheduleTest(
      GroupScheduleTestRequest groupScheduleTestRequest, String org) {
    final var testDef =
        getGroupTestDefAfterScheduleTestCreationChecks(groupScheduleTestRequest, org);
    var scheduleTest = buildGroupTestScheduleForStudents(groupScheduleTestRequest, testDef, org);
    scheduleTest.setScheduleTestStudent(
        getScheduleTestStudents(groupScheduleTestRequest.getStudentIds(), scheduleTest));
    return scheduleTest;
  }

  private TestDefinition getGroupTestDefAfterScheduleTestCreationChecks(
      GroupScheduleTestRequest scheduleTestRequest, String orgSlug) {
    var testDefinition = findTestDefinitionById(scheduleTestRequest.getTestDefinitionId());
    Set<Long> studentIdSet;
    if (Objects.nonNull(scheduleTestRequest.getSectionIds())) {
      studentIdSet = findStudentIdsForSections(scheduleTestRequest.getSectionIds(), orgSlug);
    } else {
      studentIdSet =
          findStudentIdsForAllStudents(
              scheduleTestRequest.getGrade(), orgSlug, scheduleTestRequest.getBoards());
    }
    scheduleTestRequest.setStudentIds(studentIdSet);
    return testDefinition;
  }

  private Set<Long> findStudentIdsForSections(List<Long> sectionIds, String orgSlug) {
    return new HashSet<>(userRepository.getStudentIdsOfSections(sectionIds, orgSlug));
  }

  @Transactional
  public SimpleScheduleTestResponse scheduleTest(SimpleScheduleTestRequest scheduleTestRequest) {
    final var testDef = getTestDefAfterScheduleTestCreationChecks(scheduleTestRequest);
    var scheduleTest =
        buildTestScheduleForStudents(
            scheduleTestRequest, testDef, new ScheduleTest(), Boolean.FALSE);
    scheduleTest.setScheduleTestStudent(
        getScheduleTestStudents(scheduleTestRequest.getStudentIds(), scheduleTest));
    var scheduleTestById = scheduleTestRepository.save(scheduleTest);
    saveStudentScheduleTestAnswers(scheduleTestById);
    var subjectName =
        strapiService.getSubjectNameBySlug(scheduleTestById.getTestDefinition().getSubjectSlug());
    var testNameAndSubjectName =
        (subjectName.isBlank()
                ? wrapQuotes(scheduleTestById.getTestDefinition().getTestName())
                : wrapQuotes(scheduleTestById.getTestDefinition().getTestName())
                    + " in "
                    + subjectName)
            .formatted();
    var message =
        String.format(
            NOTIFICATION_MESSAGE,
            scheduleTestById.getTestDefinition().getType() + testNameAndSubjectName);
    var students =
        scheduleTestById.getScheduleTestStudent().stream()
            .map(ScheduleTestStudent::getStudent)
            .toList()
            .stream()
            .map(User::getStudentInfo)
            .toList();
    var attributes =
        NotificationDto.TestAttributes.builder()
            .testId(scheduleTestById.getId())
            .testName(scheduleTestById.getTestDefinition().getTestName())
            .testType(scheduleTestById.getTestDefinition().getType().name())
            .subject(subjectName)
            .build();
    notificationUtils.sendNotification(
        message, students, scheduleTestById.getOrgSlug(), attributes);
    return scheduleTestTransformer.mapTestScheduleToScheduleTestResponse(scheduleTestById);
  }

  public void saveStudentScheduleTestAnswers(ScheduleTest scheduleTest) {

    List<TestScheduleStudentAnswer> studentScheduleTestAnswers = new ArrayList<>();
    final List<TestQuestion> testQuestions =
        getTestQuestionsForTestDefinition(scheduleTest.getTestDefinition());
    scheduleTest
        .getScheduleTestStudent()
        .forEach(
            tss ->
                testQuestions.forEach(
                    testQuestion ->
                        studentScheduleTestAnswers.add(
                            TestScheduleStudentAnswer.builder()
                                .attemptStatus(StudentTestAttemptStatus.NOT_VISITED)
                                .userName(tss.getStudent().getUserName())
                                .questionType(QuestionType.getByType(testQuestion.getType()))
                                .tssUuid(tss.getUuid())
                                .questionUuid(testQuestion.getQuestionUuid())
                                .testQuestionId(testQuestion.getId())
                                .sectionId(testQuestion.getTestDefinitionSection().getId())
                                .category(scheduleTest.getTestDefinition().getCategory())
                                .build())));
    studentScheduleTestAnswerRepository.saveAll(studentScheduleTestAnswers);
  }

  @SneakyThrows
  public ScheduleTest findTestScheduleById(long testScheduleId) {
    return scheduleTestRepository
        .findById(testScheduleId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.TestSchedule",
                    new String[] {Long.toString(testScheduleId)}));
  }

  private TestDefinition getTestDefAfterScheduleTestCreationChecks(
      SimpleScheduleTestRequest scheduleTestRequest) {
    validateAllStudentsBelongToTeacherOrg(scheduleTestRequest.getStudentIds());

    var testDefinition = findTestDefinitionById(scheduleTestRequest.getTestDefinitionId());

    if (scheduleTestRequest.isAllStudents()) {
      String userOrganization = authService.getUserDetails().getOrganization();
      Set<Long> studentIdSet =
          findStudentIdsForAllStudents(
              testDefinition.getGradeSlug(),
              userOrganization,
              List.of(testDefinition.getBoardSlug()));
      scheduleTestRequest.setStudentIds(studentIdSet);
    }
    validateIfStudentsAreAvailableForSchedule(scheduleTestRequest);
    return testDefinition;
  }

  public SimpleScheduleTestResponse editScheduleTest(
      long testScheduleId, SimpleScheduleTestRequest scheduleTestRequest) {

    List<Long> scheduleTestIds = new ArrayList<>();
    scheduleTestIds.add(testScheduleId);
    var childTestScheduleIds = scheduleTestRepository.findChildTestScheduleIds(testScheduleId);
    scheduleTestIds.addAll(childTestScheduleIds);
    var scheduleTests = scheduleTestRepository.findAllById(scheduleTestIds);

    scheduleTests.stream()
        .forEach(
            scheduleTest -> {
              scheduleTest.setStartDate(
                  dateTimeUtil.convertEpochToIso8601Legacy(scheduleTestRequest.getStartDate()));
              scheduleTest.setEndDate(
                  dateTimeUtil.convertEpochToIso8601Legacy(scheduleTestRequest.getEndDate()));
            });
    scheduleTestRepository.saveAll(scheduleTests);
    return scheduleTestTransformer.mapTestScheduleToScheduleTestResponse(scheduleTests.getFirst());
  }

  private Set<Long> getAllStudentsInGrade(
      String gradeSlug, String userOrganization, List<String> boardSlugs) {
    List<Integer> boardIds = new ArrayList<>();
    if (boardSlugs != null && !boardSlugs.isEmpty()) {
      boardSlugs.forEach(board -> boardIds.add(strapiService.getEduBoardBySlug(board).getId()));
    }
    var gradeId = contentService.getGradeBySlug(gradeSlug).getId();
    var allUsersByGradeAndOrganization =
        userRepository.findAllUsersByGradeAndOrganization(gradeId, userOrganization, boardIds);
    log.info(
        "There are around ["
            + allUsersByGradeAndOrganization.size()
            + "] users in Grade ["
            + gradeSlug
            + "] for ["
            + userOrganization
            + "] Organization");
    return new HashSet<>(allUsersByGradeAndOrganization);
  }

  private Set<Long> findStudentIdsForAllStudents(
      String gradeSlug, String userOrganization, List<String> boardSlugs) {
    return getAllStudentsInGrade(gradeSlug, userOrganization, boardSlugs);
  }

  private ScheduleTest buildTestScheduleForStudents(
      SimpleScheduleTestRequest scheduleTestReq,
      TestDefinition testDef,
      ScheduleTest scheduleTest,
      Boolean isBetExam) {

    scheduleTest.setStartDate(
        dateTimeUtil.convertEpochToIso8601Legacy(scheduleTestReq.getStartDate()));
    scheduleTest.setEndDate(dateTimeUtil.convertEpochToIso8601Legacy(scheduleTestReq.getEndDate()));
    scheduleTest.setStatus(ACTIVE);
    scheduleTest.setMessage(scheduleTestReq.getMessage());
    scheduleTest.setDuration(scheduleTestReq.getDuration());
    scheduleTest.setAllStudents(scheduleTestReq.isAllStudents());
    scheduleTest.setTestDefinition(testDef);
    scheduleTest.setMetadata(scheduleTestReq.getMetadata());
    scheduleTest.setTeacher(
        Boolean.TRUE.equals(isBetExam) ? getAdminTeacher() : authService.getTeacherDetails());
    scheduleTest.setPublished(
        ObjectUtils.isEmpty(scheduleTestReq.getPublished())
            ? "false"
            : scheduleTestReq.getPublished());
    scheduleTest.setType(
        ObjectUtils.isEmpty(scheduleTestReq.getType())
            ? Constants.DEFAULT_EXAM_SCHEDULETYPE
            : scheduleTestReq.getType());
    scheduleTest.setOrgSlug(scheduleTestReq.getOrgSlug());
    return scheduleTest;
  }

  private ScheduleTest buildGroupTestScheduleForStudents(
      GroupScheduleTestRequest scheduleTestReq, TestDefinition testDef, String orgSlug) {
    var scheduleTest = new ScheduleTest();
    scheduleTest.setStartDate(
        dateTimeUtil.convertEpochToIso8601Legacy(scheduleTestReq.getStartDate()));
    scheduleTest.setEndDate(dateTimeUtil.convertEpochToIso8601Legacy(scheduleTestReq.getEndDate()));
    scheduleTest.setStatus(ACTIVE);
    scheduleTest.setMessage("Good Luck!");
    scheduleTest.setDuration(scheduleTestReq.getDuration());
    scheduleTest.setAllStudents(true);
    scheduleTest.setTestDefinition(testDef);
    scheduleTest.setMetadata(constructScheduleTestMetadata(scheduleTestReq, orgSlug));
    scheduleTest.setTeacher(authService.getTeacherDetails());
    scheduleTest.setPublished("false");
    scheduleTest.setType(Constants.DEFAULT_GROUP_EXAM_SCHEDULETYPE);
    scheduleTest.setOrgSlug(orgSlug);
    return scheduleTest;
  }

  private ScheduleTestMetadata constructScheduleTestMetadata(
      GroupScheduleTestRequest scheduleTestReq, String orgSlug) {
    return ScheduleTestMetadata.builder()
        .board(scheduleTestReq.getBoard())
        .grade(scheduleTestReq.getGrade())
        .sections(getSectionsForGradeAndOrg(scheduleTestReq, orgSlug))
        .build();
  }

  private List<String> getSectionsForGradeAndOrg(
      GroupScheduleTestRequest scheduleTestRequest, String orgSlug) {
    if (Objects.nonNull(scheduleTestRequest.getSectionIds())) {
      return sectionRepository.findAllByIdIn(scheduleTestRequest.getSectionIds()).stream()
          .map(Section::getName)
          .toList();
    }
    return sectionRepository
        .getSectionsUsingGradeSlugs(
            Collections.singletonList(scheduleTestRequest.getGrade()), orgSlug)
        .stream()
        .map(Section::getName)
        .toList();
  }

  public List<ScheduleTestStudent> getScheduleTestStudents(
      Set<Long> studentIds, ScheduleTest scheduleTest) {
    var allStudentUsers = userRepository.findAllById(new ArrayList<>(studentIds));
    var scheduleTestStudentList = new ArrayList<ScheduleTestStudent>();
    for (User student : allStudentUsers) {
      var scheduleTestStudent = addTestScheduleStudentData(scheduleTest, student);
      scheduleTestStudentList.add(scheduleTestStudent);
    }
    return scheduleTestStudentList;
  }

  private void validateIfStudentsAreAvailableForSchedule(
      SimpleScheduleTestRequest scheduleTestRequest) {
    if (scheduleTestRequest.getStudentIds().isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Students.ScheduleTest");
    }
  }

  @SneakyThrows
  public TestDefinition findTestDefinitionById(long testDefId) {
    return testDefinitionRepository
        .findById(testDefId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.TestDefinition1",
                    new String[] {Long.toString(testDefId)}));
  }

  private void validateAllStudentsBelongToTeacherOrg(Set<Long> studentIds) {
    if (studentIds.isEmpty()) {
      return;
    }
    String userOrganization = authService.getUserDetails().getOrganization();

    var countOfStudentsBelongingToOrg =
        userRepository.countOfStudentsBelongingToOrg(userOrganization, new ArrayList<>(studentIds));
    if (countOfStudentsBelongingToOrg != studentIds.size()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Test.Unauthorized");
    }
  }

  public List<ScheduleTestResponse> getScheduledTests(
      GetScheduledTestsRequest getScheduledTestsRequest) throws ApiException {

    var getMockTests =
        scheduleTestRepository.findMockTestsByOrgSlug(
            getScheduledTestsRequest.getOrgSlug(), getScheduledTestsRequest.getGrade());

    if (TestType.MOCK_TEST.equals(getScheduledTestsRequest.getTestType())) {
      return getMockTests.stream()
          .map(this::buildScheduleTestResponse)
          .sorted(Comparator.comparing(ScheduleTestResponse::getEndDate).reversed())
          .toList();
    }

    var scheduledTests = getScheduleTestsBasedOnOrg(getScheduledTestsRequest);

    if (!getMockTests.isEmpty()) {
      scheduledTests.addAll(getMockTests);
    }
    if (getScheduledTestsRequest.getOrgSlug().equals(Constants.WEXL_INTERNAL)) {
      scheduledTests =
          scheduledTests.stream().filter(scheduleTest -> scheduleTest.getParent() == null).toList();
    }

    var scheduledTestsData =
        scheduledTests.stream()
            .map(this::buildScheduleTestResponse)
            .distinct()
            .sorted(Comparator.comparing(ScheduleTestResponse::getEndDate).reversed())
            .toList();

    return filterData(scheduledTestsData, getScheduledTestsRequest);
  }

  private List<ScheduleTestResponse> filterData(
      List<ScheduleTestResponse> data, GetScheduledTestsRequest getScheduledTestsRequest) {
    if (getScheduledTestsRequest.getSubject() != null
        && !getScheduledTestsRequest.getSubject().isEmpty()) {
      return data.stream()
          .filter(d -> Objects.equals(d.getSubjectName(), getScheduledTestsRequest.getSubject()))
          .toList();
    }
    return data;
  }

  private ScheduleTestResponse buildScheduleTestResponse(ScheduleTest scheduledTest) {

    var subjectSlugNameMappings =
        strapiService.getAllSubjects().parallelStream()
            .collect(Collectors.toMap(Entity::getSlug, Entity::getName));

    var gradeSlugNameMappings =
        strapiService.getAllGrades().parallelStream()
            .collect(Collectors.toMap(Grade::getSlug, Grade::getName));

    var testDefinition = scheduledTest.getTestDefinition();
    var scheduledTestResponse = transformToScheduleTestResponse(scheduledTest);
    scheduledTestResponse.setSubjectName(
        subjectSlugNameMappings.get(testDefinition.getSubjectSlug()));
    scheduledTestResponse.setGradeName(gradeSlugNameMappings.get(testDefinition.getGradeSlug()));
    scheduledTestResponse.setAssignedStudents(new ArrayList<>());
    scheduledTestResponse.setStatus(
        getScheduleTestResponseStatus(
                scheduledTest,
                scheduledTestResponse.getStartDate(),
                scheduledTestResponse.getEndDate())
            .toString());
    scheduledTestResponse.setStudentsAttempted(
        testDefinition.getType().equals(TestType.MOCK_TEST)
            ? setStudentsAttempted(scheduledTest)
            : Boolean.FALSE);
    scheduledTestResponse.setSection(
        (scheduledTest.getMetadata() == null) ? null : scheduledTest.getMetadata().getSections());
    var qpGenRecord = qpGenRepository.getDetailsByTestDefinationId(testDefinition.getId());
    scheduledTestResponse.setQpGenPresent(qpGenRecord.isPresent());
    return scheduledTestResponse;
  }

  private Boolean setStudentsAttempted(ScheduleTest scheduleTest) {
    List<String> status = List.of(COMPLETED, SUBMITTED);
    var count = scheduleTestStudentRepository.countByScheduleTestAndStatusIn(scheduleTest, status);
    return count > 0 ? Boolean.TRUE : Boolean.FALSE;
  }

  private List<ScheduleTest> getScheduleTestsBasedOnOrg(
      GetScheduledTestsRequest getScheduledTestsRequest) {
    var subjectSlug = getScheduledTestsRequest.getSubject();
    var gradeSlug = getScheduledTestsRequest.getGrade();
    var boardSlug = getScheduledTestsRequest.getBoard();
    var orgSlug = getScheduledTestsRequest.getOrgSlug();
    var type = getScheduledTestsRequest.getType();
    var teachingSubjects =
        authService.getTeacherDetails().getTeacherInfo().getMetadata().getSubjects();
    List<String> types =
        type.equals(Strings.EMPTY)
            ? List.of(DEFAULT_EXAM_SCHEDULETYPE, DEFAULT_GROUP_EXAM_SCHEDULETYPE)
            : List.of(type);
    if (StringUtils.isBlank(subjectSlug)
        && StringUtils.isBlank(gradeSlug)
        && StringUtils.isBlank(boardSlug)) {
      return scheduleTestRepository.getScheduledTestsOrg(
          orgSlug,
          teachingSubjects,
          types,
          PageRequest.of(0, 100, Sort.by("endDate").descending()));
    }

    return scheduleTestRepository.getScheduledTestsByBoardSubjectAndGradeAndOrg(
        orgSlug,
        boardSlug,
        subjectSlug,
        gradeSlug,
        teachingSubjects,
        types,
        PageRequest.of(0, 100, Sort.by("end_date").descending()));
  }

  public ScheduleTestResponse getScheduledTest(ScheduleTest scheduledTest) {
    long teacherUserId = authService.getTeacherDetails().getId();

    var testDefinition = scheduledTest.getTestDefinition();
    var childTestScheduleIds =
        scheduleTestRepository.findChildTestScheduleIds(scheduledTest.getId());

    List<Long> scheduleTestIds = new ArrayList<>(childTestScheduleIds);
    scheduleTestIds.add(scheduledTest.getId());
    List<ScheduleTestStudentResponse> scheduledTestStudentsResponseOriginal =
        scheduleTestStudentRepository.getAllStudentsByScheduledIdsAndTeacherId(
            scheduleTestIds, teacherUserId);
    List<ScheduleTestStudentResponseImpl> scheduledTestStudentsResponse =
        identifyUniqueStudents(scheduledTestStudentsResponseOriginal);

    var scheduledTestResponse = transformToScheduleTestResponse(scheduledTest);
    scheduledTestResponse.setSubjectName(
        StringUtils.isBlank(testDefinition.getSubjectSlug())
            ? null
            : strapiService.getSubjectBySlug(testDefinition.getSubjectSlug()).getName());
    scheduledTestResponse.setGradeName(
        scheduledTest.getMetadata() == null
                || StringUtils.isBlank(scheduledTest.getMetadata().getGrade())
            ? null
            : String.valueOf(
                contentService.getGradeBySlug(scheduledTest.getMetadata().getGrade()).getName()));
    List<TestQuestion> testQuestions = getTestQuestionsForTestDefinition(testDefinition);
    List<ScheduleTestStudentResponseImpl> filteredAssignedStudents = new ArrayList<>();

    var unFilteredAssignedStudents =
        scheduledTestStudentsResponse.stream()
            .map(
                scheduledTestStudentResponse -> {
                  if (scheduledTestStudentResponse.getExamId() != null) {
                    var optionalExam =
                        examRepository.findById(scheduledTestStudentResponse.getExamId());
                    if (optionalExam.isPresent()) {
                      var exam = optionalExam.get();
                      scheduledTestStudentResponse.setStartTime(
                          dateTimeUtil.convertIso8601ToEpoch(
                              exam.getStartTime().toLocalDateTime()));
                      scheduledTestStudentResponse.setEndTime(
                          dateTimeUtil.convertIso8601ToEpoch(exam.getEndTime().toLocalDateTime()));
                    }
                  }
                  if (!scheduledTestResponse.getType().equals("MOCK_TEST")
                      && !scheduledTestResponse.getType().equals("SCHOOL_TEST")) {
                    scheduledTestStudentResponse.setStudentTestStatus(
                        getStudentTestStatus(
                            scheduledTestStudentResponse.getUserId(),
                            scheduledTestStudentResponse.getExamId(),
                            scheduledTestResponse.getId()));
                  }
                  List<TestDefinitionsDto.SectionsResponse> testDefinitionSections =
                      buildTestDefinitionSections(scheduledTestStudentResponse, testDefinition);
                  scheduledTestStudentResponse.setTestDefinitionSections(testDefinitionSections);
                  if (Objects.nonNull(scheduledTestStudentResponse.getExamId())) {
                    boolean isBetExam =
                        scheduledTestResponse.getTestName().toLowerCase().startsWith("bet");

                    Float totalMarksScored;

                    if (isBetExam) {

                      Float totalScore =
                          testDefinitionSections.stream()
                              .filter(section -> !"Speaking".equalsIgnoreCase(section.name()))
                              .map(TestDefinitionsDto.SectionsResponse::marksScored)
                              .reduce(0f, Float::sum);

                      Float speakingIeltsScore =
                          testDefinitionSections.stream()
                              .filter(section -> "Speaking".equalsIgnoreCase(section.name()))
                              .map(TestDefinitionsDto.SectionsResponse::ieltsScored)
                              .findFirst()
                              .get()
                              .floatValue();

                      totalMarksScored = totalScore + speakingIeltsScore;
                    } else {
                      totalMarksScored =
                          calculateMarksScored(scheduledTestStudentResponse.getExamId());
                    }

                    var exam =
                        examRepository
                            .findById(scheduledTestStudentResponse.getExamId())
                            .orElseThrow();
                    int totalMarks =
                        totalMarksForTest(testDefinition, testQuestions, exam.getTotalMarks());

                    double percentage = calculatePercentage(totalMarks, totalMarksScored);
                    scheduledTestStudentResponse.setTotalMarks(totalMarks);
                    scheduledTestStudentResponse.setMarksScored(totalMarksScored);
                    scheduledTestStudentResponse.setPercentage(percentage);
                  } else {
                    scheduledTestStudentResponse.setTotalMarks(0);
                    scheduledTestStudentResponse.setMarksScored(0f);
                    scheduledTestStudentResponse.setPercentage(0.0);
                    scheduledTestStudentResponse.setExamEvaluated(false);
                  }
                  return scheduledTestStudentResponse;
                })
            .toList();

    var filteredToCompletedCorrected =
        unFilteredAssignedStudents.stream()
            .filter(
                scheduleTestStudentResponse ->
                    scheduleTestStudentResponse.getStudentTestStatus().equals(COMPLETED))
            .filter(studentsResponse -> studentsResponse.getExamEvaluated().equals(true))
            .sorted(comparing(ScheduleTestStudentResponseImpl::getMarksScored).reversed())
            .toList();
    filteredAssignedStudents.addAll(filteredToCompletedCorrected);

    var filteredToCompletedButNotCorrected =
        unFilteredAssignedStudents.stream()
            .filter(
                scheduleTestStudentResponse ->
                    scheduleTestStudentResponse.getStudentTestStatus().equals(COMPLETED))
            .filter(studentsResponse -> studentsResponse.getExamEvaluated().equals(false))
            .toList();
    filteredToCompletedButNotCorrected.forEach(x -> x.setStudentTestStatus(NOTEVALUATED));
    filteredAssignedStudents.addAll(filteredToCompletedButNotCorrected);

    if (Objects.equals(scheduledTestResponse.getType(), "ASSIGNMENT")) {
      var filteredToCompletedNotCorrected =
          unFilteredAssignedStudents.stream()
              .filter(
                  scheduleTestStudentResponse ->
                      scheduleTestStudentResponse.getStudentTestStatus().equals(NOTEVALUATED))
              .filter(studentsResponse -> studentsResponse.getExamEvaluated().equals(false))
              .toList();
      filteredAssignedStudents.addAll(filteredToCompletedNotCorrected);
    }
    var filteredToSubmitted =
        unFilteredAssignedStudents.stream()
            .filter(
                scheduleTestStudentResponse ->
                    scheduleTestStudentResponse.getStudentTestStatus().equals(SUBMITTED))
            .toList();
    if (scheduledTestResponse.getType().equals("MOCK_TEST")
        && isSubjectiveQuestionPresent(testQuestions)) {
      filteredToSubmitted.forEach(x -> x.setStudentTestStatus(NOTEVALUATED));
    }
    filteredAssignedStudents.addAll(filteredToSubmitted);
    Set<String> allowedStatuses = Set.of(STARTED, ERRORINPROCESSING, PENDING);

    filteredAssignedStudents.addAll(
        unFilteredAssignedStudents.stream()
            .filter(student -> allowedStatuses.contains(student.getStudentTestStatus()))
            .toList());

    scheduledTestResponse.setAssignedStudents(filteredAssignedStudents);

    scheduledTestResponse.setStatus(
        getScheduleTestResponseStatus(
                scheduledTestResponse.getAssignedStudents(),
                scheduledTestResponse.getStartDate(),
                scheduledTestResponse.getEndDate())
            .toString());
    scheduledTestResponse.setTeacherName(
        scheduledTest.getTeacher().getFirstName() + " " + scheduledTest.getTeacher().getLastName());

    scheduledTestResponse.setNotificationSent(
        isNotificationSentByScheduleTest(scheduledTest.getNotificationStatus()));
    return scheduledTestResponse;
  }

  private boolean isSubjectiveQuestionPresent(List<TestQuestion> testQuestions) {
    List<TestQuestion> subjectiveQuestions =
        testQuestions.stream()
            .filter(q -> q.getType().equalsIgnoreCase(QuestionType.SUBJECTIVE.toString()))
            .toList();
    return subjectiveQuestions.isEmpty() ? Boolean.FALSE : Boolean.TRUE;
  }

  private List<TestDefinitionsDto.SectionsResponse> buildTestDefinitionSections(
      ScheduleTestStudentResponseImpl scheduledTestStudentResponse, TestDefinition testDefinition) {
    List<TestDefinitionsDto.SectionsResponse> sectionsList = new ArrayList<>();
    testDefinition
        .getTestDefinitionSections()
        .forEach(
            section -> {
              var questions = testQuestionRepository.findByTestDefinitionSectionOrderById(section);
              var marks = questions.stream().mapToLong(TestQuestion::getMarks).sum();
              var questionUuids = questions.stream().map(TestQuestion::getQuestionUuid).toList();
              sectionsList.add(
                  TestDefinitionsDto.SectionsResponse.builder()
                      .id(section.getId())
                      .noOfQuestions(section.getNoOfQuestions())
                      .totalMarks(marks)
                      .name(section.getName())
                      .sequenceNumber(section.getSequenceNumber())
                      .marksScored(
                          scheduledTestStudentResponse.getExamId() == null
                              ? null
                              : getMarksScoredBySection(
                                  scheduledTestStudentResponse, questionUuids))
                      .ieltsScored(
                          scheduledTestStudentResponse.getExamId() == null
                              ? null
                              : validationUtils.formatMarks(
                                  getMarksScoredBySection(
                                          scheduledTestStudentResponse, questionUuids)
                                      .doubleValue()))
                      .build());
            });

    return sectionsList;
  }

  public Double getIeltsScore(Long examId) {
    Double ieltsScore;
    var exam = examRepository.findById(examId).orElseThrow();

    var speechExamAnswers =
        exam.getExamAnswers().stream()
            .filter(
                q ->
                    SPCH.getType().equals(q.getType())
                        && Objects.nonNull(q.getSpchSelectedAnswer()))
            .toList();

    List<SpeechEvaluation.SpeechResponse> speechResponses =
        speechExamAnswers.stream()
            .map(
                sea ->
                    speechEvaluationService.evaluateSpeakingTest(
                        exam.getId(), sea.getQuestionUuid()))
            .filter(Objects::nonNull)
            .toList();

    var assessments =
        speechResponses.stream()
            .map(SpeechEvaluation.SpeechResponse::assessment)
            .filter(Objects::nonNull)
            .toList();

    var ieltsTotal =
        assessments.isEmpty()
            ? OptionalDouble.empty()
            : assessments.stream()
                .mapToDouble(SpeechEvaluation.PronunciationAssessment::ieltsScore)
                .average();

    var speechScores =
        speechResponses.stream()
            .map(SpeechEvaluation.SpeechResponse::speechScore)
            .filter(Objects::nonNull)
            .toList();
    var speechIeltsScore =
        speechScores.isEmpty()
            ? OptionalDouble.empty()
            : speechScores.stream().map(SpeechEvaluation.SpeechScore::ieltsScore).toList().stream()
                .mapToDouble(SpeechEvaluation.PronunciationScore::pronunciation)
                .average();

    double ieltsTotalScore;
    if (speechIeltsScore.isPresent() && ieltsTotal.isPresent()) {
      ieltsTotalScore = (speechIeltsScore.getAsDouble() + ieltsTotal.getAsDouble()) / 2;
    } else if (speechIeltsScore.isEmpty()) {
      ieltsTotalScore = ieltsTotal.orElse(0.0);
    } else {
      ieltsTotalScore = speechIeltsScore.orElse(0.0);
    }
    ieltsScore = getOverallScore(ieltsTotal.orElse(0.0));
    return ieltsScore;
  }

  public Double getOverallScore(Double score) {
    if (Objects.isNull(score)) {
      return null;
    }
    if (score >= 8.75) return 9.0;
    else if (score >= 8.25) return 8.5;
    else if (score >= 7.75) return 8.0;
    else if (score >= 7.25) return 7.5;
    else if (score >= 6.75) return 7.0;
    else if (score >= 6.25) return 6.5;
    else if (score >= 5.75) return 6.0;
    else if (score >= 5.25) return 5.5;
    else if (score >= 4.75) return 5.0;
    else if (score >= 4.25) return 4.5;
    else if (score >= 3.75) return 4.0;
    else if (score >= 3.25) return 3.5;
    else if (score >= 2.75) return 3.0;
    else if (score >= 2.25) return 2.5;
    else if (score >= 1.75) return 2.0;
    else if (score >= 1.25) return 1.5;
    else if (score >= 0.75) return 1.0;
    else if (score >= 0.25) return 0.5;
    else return 0.0;
  }

  private Float getMarksScoredBySection(
      ScheduleTestStudentResponseImpl scheduledTestStudentResponse, List<String> questionUuids) {

    final Optional<Exam> byId = examRepository.findById(scheduledTestStudentResponse.getExamId());
    if (byId.isEmpty()) {
      return 0.0f;
    }
    final Exam exam = byId.get();
    // remove Duplicate ExamAnswers By QuestionUuid;
    final Float marksBySectionForQuestions =
        exam.getExamAnswers().stream()
            .filter(examAnswer -> questionUuids.contains(examAnswer.getQuestionUuid()))
            .collect(
                Collectors.toMap(
                    ExamAnswer::getQuestionUuid,
                    Function.identity(),
                    (existing, replacement) -> existing))
            .values()
            .stream()
            .map(
                examAnswer ->
                    QuestionType.SUBJECTIVE.toString().equalsIgnoreCase(examAnswer.getType())
                            && !exam.isCorrected()
                        ? Optional.ofNullable(examAnswer.getAiMarks()).orElse(0F)
                        : examAnswer.getMarksScoredPerQuestion())
            .reduce(Float::sum)
            .orElse(0F);

    return marksBySectionForQuestions;
  }

  private List<ScheduleTestStudentResponseImpl> identifyUniqueStudents(
      List<ScheduleTestStudentResponse> scheduledTestStudentsResponses) {
    return scheduledTestStudentsResponses.stream()
        .map(
            item -> {
              var scheduleTestStudentResponseImpl = new ScheduleTestStudentResponseImpl();
              BeanUtils.copyProperties(item, scheduleTestStudentResponseImpl);
              return scheduleTestStudentResponseImpl;
            })
        .collect(
            Collectors.toMap(
                ScheduleTestStudentResponseImpl::getUserId,
                Function.identity(),
                (existing, replacement) -> existing))
        .values()
        .stream()
        .toList();
  }

  private boolean isNotificationSentByScheduleTest(Boolean notificationStatus) {
    return Objects.nonNull(notificationStatus) && notificationStatus;
  }

  private double calculatePercentage(int totalMarks, Float marksScored) {
    return (totalMarks <= 0
        ? 0.0
        : Double.parseDouble(Constants.DECIMAL_FORMAT.format(marksScored * 100 / totalMarks)));
  }

  private Float calculateMarksScored(Long examId) {
    final List<ExamAnswer> examAnswers = studentAnswerRepository.findByExamId(examId);
    var marksScoredForExam = countMarksScoredForExam(examAnswers);
    return marksScoredForExam < 0
        ? 0
        : Float.parseFloat(Constants.DECIMAL_FORMAT.format(marksScoredForExam));
  }

  public Float countMarksScoredForExam(List<ExamAnswer> examAnswers) {
    var exam = examAnswers.getFirst().getExam();
    final List<ExamAnswer> examAnswersUpdated = filterDuplicateEntries(examAnswers);
    return examAnswersUpdated.stream()
        .map(
            examAnswer ->
                !exam.isCorrected()
                        && QuestionType.SUBJECTIVE.toString().equalsIgnoreCase(examAnswer.getType())
                    ? Objects.isNull(examAnswer.getAiMarks()) ? 0.0F : examAnswer.getAiMarks()
                    : examAnswer.getMarksScoredPerQuestion())
        .reduce(Float::sum)
        .orElse(0f);
  }

  private List<ExamAnswer> filterDuplicateEntries(List<ExamAnswer> examAnswers) {
    return examAnswers.stream()
        .collect(
            Collectors.toMap(
                ExamAnswer::getQuestionUuid,
                Function.identity(),
                (existing, replacement) -> existing))
        .values()
        .stream()
        .toList();
  }

  private int totalMarksForTest(
      TestDefinition testDefinition, List<TestQuestion> questions, Float marks) {
    int totalMarks =
        competitiveExamValidatorProcessor.getTotalMarks(testDefinition.getCategory(), marks);
    if (totalMarks == -1) {
      return questions.stream().map(TestQuestion::getMarks).reduce(Integer::sum).orElse(0);
    }

    return totalMarks;
  }

  private ScheduleTestResponse transformToScheduleTestResponse(ScheduleTest scheduleTest) {
    final var testDefinition = scheduleTest.getTestDefinition();
    return ScheduleTestResponse.builder()
        .id(scheduleTest.getId())
        .testName(testDefinition.getTestName())
        .duration(scheduleTest.getDuration())
        .testdefinationId(testDefinition.getId())
        .startDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getStartDate()))
        .endDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getEndDate()))
        .message(scheduleTest.getMessage())
        .type(
            Objects.requireNonNullElse(
                    scheduleTest.getTestDefinition().getType(), TestType.SCHEDULED_TEST)
                .toString())
        .scheduleTestType(
            Objects.requireNonNullElse(scheduleTest.getType(), DEFAULT_EXAM_SCHEDULETYPE))
        .build();
  }

  public GenericResponse deleteScheduleTestById(long testScheduleId) {
    validateScheduleStatus(testScheduleId);
    var childTestScheduleIds = scheduleTestRepository.findChildTestScheduleIds(testScheduleId);
    List<Long> scheduleTestIds = new ArrayList<>();
    scheduleTestIds.addAll(childTestScheduleIds);
    scheduleTestIds.add(testScheduleId);
    scheduleTestRepository.deleteAllById(scheduleTestIds);
    return GenericResponse.builder().status(true).message("Deleted successfully!").build();
  }

  private void validateScheduleStatus(long testScheduleId) {
    ScheduleTest scheduleTest =
        scheduleTestRepository
            .findById(testScheduleId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestSchedule1"));

    String scheduledTestStatus =
        getScheduledTestStatus(scheduleTest.getStartDate(), scheduleTest.getEndDate());
    log.info(
        "The scheduled test status for schedule ["
            + testScheduleId
            + "] is ["
            + scheduledTestStatus
            + "]");

    if (!UPCOMING.equals(scheduledTestStatus)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Test.Delete",
          new String[] {scheduledTestStatus});
    }
  }

  public ScheduleTestStudent addTestScheduleStudentData(ScheduleTest scheduleTest, User student) {
    var testScheduleStudent = new ScheduleTestStudent();
    testScheduleStudent.setScheduleTest(scheduleTest);
    testScheduleStudent.setStudent(student);
    testScheduleStudent.setStatus(Constants.PENDING);
    testScheduleStudent.setUuid(UUID.randomUUID().toString());
    testScheduleStudent.setQuestionResponseSet(randomNumber());
    testScheduleStudent.setResultProcessingTime(
        LocalDateTime.now().plusMinutes(scheduleTest.getDuration()));
    return testScheduleStudent;
  }

  public static int randomNumber() {
    return rand.nextInt(0, 5);
  }

  public ScheduleTestStatus getScheduleTestResponseStatus(
      ScheduleTest scheduleTest, long testStartDate, long testEndDate) {
    var currentDate = LocalDateTime.now();
    var status = ScheduleTestStatus.IN_PROGRESS;
    if (currentDate.isAfter(dateTimeUtil.convertEpochToIso8601Legacy(testEndDate))) {
      long examsNotCorrectedCount =
          scheduleTestStudentRepository.getExamsNotCorrectedCount(scheduleTest);
      if (examsNotCorrectedCount == 0) {
        status = ScheduleTestStatus.EVALUATED;
      } else {
        status = ScheduleTestStatus.TO_BE_EVALUATED;
      }
    } else if (currentDate.isBefore(dateTimeUtil.convertEpochToIso8601Legacy(testStartDate))) {
      status = ScheduleTestStatus.SCHEDULED;
    }
    return status;
  }

  private ScheduleTestStatus getScheduleTestResponseStatus(
      List<ScheduleTestStudentResponseImpl> assignedStudents,
      long testStartDate,
      long testEndDate) {
    var currentDate = LocalDateTime.now();
    var status = ScheduleTestStatus.IN_PROGRESS;
    if (currentDate.isAfter(dateTimeUtil.convertEpochToIso8601Legacy(testEndDate))) {
      List<ScheduleTestStudentResponseImpl> unevaluatedStudents =
          assignedStudents.parallelStream()
              .filter(
                  assignedStudent ->
                      Boolean.TRUE.equals(assignedStudent.getTestTaken())
                          && Boolean.FALSE.equals(assignedStudent.getExamEvaluated()))
              .toList();
      if (CollectionUtils.isEmpty(unevaluatedStudents)) {
        status = ScheduleTestStatus.EVALUATED;
      } else {
        status = ScheduleTestStatus.TO_BE_EVALUATED;
      }
    } else if (currentDate.isBefore(dateTimeUtil.convertEpochToIso8601Legacy(testStartDate))) {
      status = ScheduleTestStatus.SCHEDULED;
    }
    return status;
  }

  public String getScheduledTestStatus(LocalDateTime startDate, LocalDateTime endDate) {
    var currentDate = LocalDateTime.now();
    if (currentDate.isAfter(endDate)) {
      return EXPIRED;
    }
    if (currentDate.isBefore(startDate)) {
      return UPCOMING;
    }
    return INPROGRESS;
  }

  private String getStudentTestStatus(Long userId, Long examId, Long scheduleId) {

    if (examId == null) {
      long count =
          examRepository.getPartialTestTakenCountByStudentId(
              userService.findUserById(userId).getStudentInfo(), findTestScheduleById(scheduleId));

      return count == 0 ? PENDING : STARTED;
    }
    Exam exam =
        examRepository
            .findById(examId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.ExamNotFound"));
    return (Boolean.TRUE.equals(exam.isCorrected()) ? COMPLETED : NOTEVALUATED);
  }

  public List<TestQuestion> getTestQuestionsForTestDefinition(TestDefinition testDefinition) {
    // Duplicated code to avoid circular dependency.. Will try to fix it in next build
    final List<TestDefinitionSection> testDefinitionSections =
        testDefinition.getTestDefinitionSections();
    final Optional<List<TestQuestion>> testQuestionList =
        testDefinitionSections.stream()
            .map(TestDefinitionSection::getTestQuestions)
            .reduce(
                (testQuestions, testQuestions2) -> {
                  testQuestions.addAll(testQuestions2);
                  return testQuestions;
                });

    return testQuestionList.isEmpty() ? new ArrayList<>() : testQuestionList.get();
  }

  public ScheduleTest scheduleBetTest(
      TestDefinition testDefinition, SimpleScheduleTestRequest request, User user) {
    List<ScheduleTestStudent> studentList = new ArrayList<>();
    var scheduleTest =
        buildTestScheduleForStudents(request, testDefinition, new ScheduleTest(), Boolean.TRUE);
    var students = addTestScheduleStudentData(scheduleTest, user);
    studentList.add(students);
    scheduleTest.setScheduleTestStudent(studentList);
    saveStudentScheduleTestAnswers(scheduleTest);
    return scheduleTestRepository.save(scheduleTest);
  }

  public User getAdminTeacher() {
    var user = authService.getUserDetails();
    var orgSlug = user.getOrganization();
    var adminTeachers = teacherRepository.getAllAdminsByOrg(orgSlug);
    return adminTeachers.getFirst().getUserInfo();
  }

  public List<TestScheduleReportResponse.Response> getTestScheduleReports(
      List<String> orgSlugs,
      List<String> board,
      List<String> grades,
      List<String> sectionUuids,
      String fromDate,
      String toDate) {

    String fDate =
        String.valueOf(dateTimeUtil.repositoryFriendlyDateFromEpoch(Long.valueOf(fromDate)));
    String tDate =
        String.valueOf(dateTimeUtil.repositoryFriendlyDateFromEpoch(Long.valueOf(toDate)));

    var tests =
        testDefinitionRepository.getTestDetails(
            orgSlugs, board, grades, sectionUuids, fDate, tDate);

    var section =
        sectionRepository
            .findByUuid(UUID.fromString(sectionUuids.get(0))) // Using get(0) instead of getFirst()
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.sectionNotFound"));

    List<TestScheduleReportResponse.Response> responses = new ArrayList<>();

    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");

    for (TestSchedulesReportInterface test : tests) {

      var scheduleTestMetadata =
          scheduleTestRepository.findById(test.getScheduleId()).get().getMetadata();

      boolean sectionMatch = scheduleTestMetadata.getSections().contains(section.getName());

      if (!sectionMatch) {
        continue;
      }

      LocalDate date = LocalDate.parse(test.getDate(), inputFormatter);
      String formattedDate = date.format(outputFormatter);

      TestScheduleReportResponse.Response response =
          TestScheduleReportResponse.Response.builder()
              .testName(test.getTestName())
              .scheduledBy(test.getScheduledBy())
              .scheduleId(test.getScheduleId())
              .date(formattedDate)
              .teacherName(test.getTeacherName())
              .testDefinitionId(test.getTestDefinitionId())
              .gradeSlug(test.getGradeSlug())
              .gradeName(test.getGradeName())
              .sectionName(test.getSectionName())
              .build();

      responses.add(response);
    }

    return responses;
  }

  public ScheduleTest validateTestSchedule(Long id) {
    return scheduleTestRepository
        .findById(id)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TestSchedule"));
  }
}
