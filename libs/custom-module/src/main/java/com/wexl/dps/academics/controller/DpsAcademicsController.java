package com.wexl.dps.academics.controller;

import com.wexl.dps.academics.service.DpsAcademicsService;
import com.wexl.dps.mlp.dto.MlpDto;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/dps-academics")
@RequiredArgsConstructor
public class DpsAcademicsController {

  private final DpsAcademicsService dpsAcademicsService;

  @GetMapping()
  public List<MlpDto.Subject> getAcademicsData(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam String boardSlug,
      @RequestParam(required = false) String childOrg,
      @RequestParam(required = false) String gradeSlug) {
    return dpsAcademicsService.getAcademicsData(orgSlug, boardSlug, gradeSlug, childOrg);
  }
}
