package com.wexl.retail.util;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.wexl.retail.auth.dto.MobileNumberLoginDto;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.documents.model.Document;
import com.wexl.retail.documents.repository.DocumentRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.task.domain.Task;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.repository.TaskInstRepository;
import com.wexl.retail.task.repository.TaskRepository;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestQuestion;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.repository.TestDefinitionSectionRepository;
import com.wexl.retail.test.school.repository.TestQuestionRepository;
import java.text.DecimalFormat;
import java.util.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Data
@Slf4j
@Component
public class ValidationUtils {

  private final UserRepository userRepository;

  private final DocumentRepository documentRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final OrganizationRepository organizationRepository;

  private final ClassroomRepository classroomRepository;
  private final TestDefinitionSectionRepository testDefinitionSectionRepository;
  private final TestQuestionRepository testQuestionRepository;
  private final TaskRepository taskRepository;
  private final TaskInstRepository taskInstRepository;
  private final ExamRepository examRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final StrapiService strapiService;

  public User isValidUser(String authUserId) {
    var optionalUser = userRepository.findByAuthUserId(authUserId);
    if (optionalUser.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound");
    }
    return optionalUser.get();
  }

  public Document checkIfDocumentExist(Long documentId) {
    var document = documentRepository.findById(documentId);

    if (document.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND,
          "error.Invalid.Document",
          new String[] {documentId.toString()});
    }
    return document.get();
  }

  public Student isStudentValid(Long studentId) {
    var optionalStudent = studentRepository.findById(studentId);
    if (optionalStudent.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound");
    }
    return optionalStudent.get();
  }

  public Section findSectionByUuid(String sectionUuid) {
    Optional<Section> optionalSection = sectionRepository.findByUuid(UUID.fromString(sectionUuid));
    if (optionalSection.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.SectionFind.Grade");
    }
    return optionalSection.get();
  }

  public ScheduleTest isTestScheduleValid(Long scheduleId) {
    var optionalScheduleTest = scheduleTestRepository.findById(scheduleId);
    if (optionalScheduleTest.isEmpty()) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.TestSchedule");
    }
    return optionalScheduleTest.get();
  }

  public Organization isOrgValid(String orgSlug) {
    var organization = organizationRepository.findBySlug(orgSlug);
    if (organization == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.OrganizationFind.Slug",
          new String[] {orgSlug});
    }
    return organization;
  }

  public Classroom isClassroomValid(Long id) {
    var classroom = classroomRepository.findById(id);
    if (classroom.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ClassRoomValidity.ClassIdAndOrg",
          new String[] {Long.toString(id), "aft620984"});
    }
    return classroom.get();
  }

  public TestDefinitionSection getTestDefinitionSectionById(Long sectionId) {
    var testDefinitionSection = testDefinitionSectionRepository.findById(sectionId);

    if (testDefinitionSection.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND,
          "error.TestDefinitionSection",
          new String[] {Long.toString(sectionId)});
    }
    return testDefinitionSection.get();
  }

  public List<TestQuestion> getTestQuestionByTestDefinitionSection(
      TestDefinitionSection testDefinitionSection) {
    var testQuestions =
        testQuestionRepository.findByTestDefinitionSectionOrderById(testDefinitionSection);

    if (testQuestions.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Invalid TestQuestion");
    }
    return testQuestions;
  }

  public Task getTaskById(Long taskId) {
    var optionalTask = taskRepository.findById(taskId);

    if (optionalTask.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid TaskId");
    }
    return optionalTask.get();
  }

  public TaskInst getTaskInstById(Long taskInstId) {
    var optionalTaskInst = taskInstRepository.findByIdAndDeletedAtIsNull(taskInstId);

    if (optionalTaskInst.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid TaskInstId");
    }
    return optionalTaskInst.get();
  }

  public Exam findByExamId(Long examId) {
    var optionalExam = examRepository.findById(examId);

    if (optionalExam.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.ExamId", new String[] {Long.toString(examId)});
    }
    return optionalExam.get();
  }

  public TestDefinition validateTestDefinition(Long testDefinitionId) {
    var optionalTestDefinition = testDefinitionRepository.findById(testDefinitionId);

    if (optionalTestDefinition.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.TestDefinition1",
          new String[] {Long.toString(testDefinitionId)});
    }
    return optionalTestDefinition.get();
  }

  public Grade getStudentGrade(Student student) {
    List<Grade> allGrades = new ArrayList<>(strapiService.getAllGrades());
    final Optional<Grade> studentGrade =
        allGrades.stream().filter(grade -> grade.getId() == student.getClassId()).findFirst();
    if (studentGrade.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Student grade not found in strapi",
          new String[] {Long.toString(student.getClassId())});
    }
    return studentGrade.get();
  }

  public Grade findGradeBySlug(List<Grade> gradesList, String gradeSlug) {
    var grade = gradesList.stream().filter(x -> x.getSlug().equals(gradeSlug)).findAny();
    if (grade.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid Grade");
    }

    return grade.get();
  }

  public Entity findBoardBySlug(List<Entity> boardList, String boardSlug) {
    var board = boardList.stream().filter(x -> x.getSlug().equals(boardSlug)).findAny();
    if (board.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid Board");
    }

    return board.get();
  }

  public Student validateStudentByAuthId(String authUserId, String orgSlug) {
    Student student = studentRepository.getStudentByAuthUserIdAndOrgSlug(authUserId, orgSlug);
    if (Objects.nonNull(student)) {
      return student;
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound");
  }

  public double formatMarks(double percentage) {
    DecimalFormat decimalFormat = new DecimalFormat("0.0");
    return Double.parseDouble(decimalFormat.format(percentage));
  }

  public String validateMobileNumber(MobileNumberLoginDto.MobileNumberLoginOtpRequest request) {
    PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();

    Phonenumber.PhoneNumber phoneNumber;
    try {
      phoneNumber = phoneNumberUtil.parse(request.mobileNumber(), request.countryCode());
    } catch (NumberParseException e) {
      log.error("Invalid phone number format: {}", request.mobileNumber(), e);
      return null;
    }

    if (!phoneNumberUtil.isValidNumber(phoneNumber)) {
      return null;
    }
    return phoneNumberUtil.format(phoneNumber, PhoneNumberUtil.PhoneNumberFormat.E164);
  }
}
