package com.wexl.retail.repository;

import com.wexl.retail.model.Badge;
import com.wexl.retail.model.UserRole;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface BadgeRepository extends JpaRepository<Badge, Long> {
  Badge findFirstBySlug(String slug);

  @Query(
      value =
          "select ub,b from Badge b left join UserBadge ub on ub.badge = b and ub.user.id = ?1 where (ub.expiryDate is null or ub.expiryDate > CURRENT_TIMESTAMP )")
  List<Object[]> getAllStudentBadges(long userId, UserRole userRole);
}
