package com.wexl.retail.omr;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.test.school.domain.TestCategory;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import lombok.Builder;

@Builder
public record OmrDto() {
  @Builder
  public record TestScheduleRequest(
      @JsonProperty("file_reference") String fileReference,
      @JsonProperty("test_schedule_id") Long testScheduleId,
      @JsonProperty("extension") String extension) {}

  @Builder
  public record OmrRequest(
      @NotNull @JsonProperty("test_schedule_id") Long testScheduleId,
      @NotNull @JsonProperty("form_name") String formName) {}

  @Builder
  public record OmrProcessingResponse(
      @NotNull @JsonProperty("test_schedule_id") Long testScheduleId,
      @NotNull @JsonProperty("form_name") String formName,
      @JsonProperty("file_path") String filePath,
      String orgSlug,
      @JsonProperty("bucket_name") String bucketName,
      @JsonProperty("namespace") String ns) {}

  @Builder
  public record TestScheduleResponse(
      String rollNumber, String userName, Map<Integer, String> selectedQuestions) {}

  @Builder
  public record OmrProcessResult(
      @NotNull @JsonProperty("student_roll_number") String studentRollNumber,
      @NotNull @JsonProperty("test_schedule_id") Long testScheduleId,
      @JsonProperty("set_number") Integer setNumber,
      @JsonProperty("image_path") String omrImagePath,
      @NotNull @JsonProperty("question_result") List<QuestionResult> result) {}

  @Builder
  public record QuestionResult(
      @JsonProperty("question_type") String questionType,
      @JsonProperty("question_number") String questionNumber,
      @JsonProperty("question_answer") String questionAnswer) {}

  @Builder
  public record OmrTaskResponse(
      @JsonProperty("created_at") Long createdAt,
      String status,
      @JsonProperty("failure_reason") String failureReason,
      @JsonProperty("form_name") String formName,
      @JsonProperty("omr_task_details") List<OmrTaskDetailResponse> omrTaskDetailResponse) {}

  @Builder
  public record OmrTaskDetailResponse(
      @JsonProperty("file_name") String fileName,
      String status,
      @JsonProperty("last_updated_at") Long lastUpdatedAt,
      @JsonProperty("failure_reason") String failureReason,
      String result,
      @JsonProperty("image_path") String imagePath,
      @JsonProperty("student_roll_number") String studentRollNumber) {}

  @Builder
  public record OmrRetailTaskResponse(
      @JsonProperty("created_at") Long createdAt,
      String status,
      @JsonProperty("failure_reason") String failureReason,
      @JsonProperty("omr_task_details") List<OmrRetailTaskDetailResponse> omrTaskDetails) {}

  @Builder
  public record OmrRetailTaskDetailResponse(
      String status,
      @JsonProperty("last_updated_at") Long lastUpdatedAt,
      @JsonProperty("failure_reason") String failureReason,
      String result,
      @JsonProperty("image_url") String imageUrl,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("student_user_name") String studentUserName,
      @JsonProperty("roll_number") String rollNumber,
      @JsonProperty("student_id") Long studentId) {}

  @Builder
  public record OmrTemplateDownloadResponse(
      @JsonProperty("form_type") TestCategory fileType, String url) {}
}
