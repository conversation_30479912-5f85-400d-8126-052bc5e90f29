package com.wexl.erp.paymentGateway.types;

import com.razorpay.RazorpayClient;
import com.razorpay.RazorpayException;
import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.paymentGateway.dto.PaymentGatewayDto;
import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import com.wexl.erp.paymentGateway.model.PaymentGatewayDetail;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.binary.Hex;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RazorPay implements PaymentGatewayRule {
  @Override
  public boolean supports(PaymentMethod paymentMethod) {
    return PaymentMethod.RAZOR_PAYMENT.equals(paymentMethod);
  }

  @Override
  public PaymentGatewayDto.Response initiatePayment(
      String orgSlug,
      FeeDto.CollectFeeRequest request,
      FeeHead feeHead,
      PaymentGatewayDetail config) {

    final var rzPayOrder = createRzPayOrder(config, request, feeHead);

    return PaymentGatewayDto.Response.builder()
        .referenceId(rzPayOrder.get("id").toString())
        .jsonObject(rzPayOrder.toJson())
        .build();
  }

  private com.razorpay.Order createRzPayOrder(
      PaymentGatewayDetail payment, FeeDto.CollectFeeRequest request, FeeHead feeHead) {
    try {
      var razorpay =
          new RazorpayClient(payment.getConfig().getKeyId(), payment.getConfig().getSecretKey());
      var totalAmount = request.amount();
      JSONObject orderRequest = new JSONObject();
      orderRequest.put("amount", totalAmount);
      orderRequest.put("currency", "INR");
      orderRequest.put("receipt", "txn_%s".formatted(feeHead.getId()));
      orderRequest.put("payment_capture", 1);
      JSONObject notes = new JSONObject();
      var student = feeHead.getStudent();
      notes.put("date", LocalDateTime.now().toString());
      notes.put("email", student.getUserInfo().getEmail());
      notes.put("mobile_number", student.getUserInfo().getMobileNumber());
      orderRequest.put("notes", notes);
      orderRequest.put("amount", totalAmount);
      return razorpay.orders.create(orderRequest);
    } catch (RazorpayException e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "rzPayOrderError", e);
    }
  }

  @Override
  public void verifyPayment(
      String orgSlug,
      String paymentId,
      PaymentGatewayDto.VerifyPaymentRequest request,
      PaymentGatewayDetail paymentGatewayDetail) {
    try {
      String generatedSignature =
          hmacSha256(
              "%s|%s".formatted(request.razorpayOrderId(), request.razorpayPaymentId()),
              paymentGatewayDetail);
      if (!generatedSignature.equals(request.razorpaySignature())) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "Payment failed due to invalid signature");
      }
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private String hmacSha256(String data, PaymentGatewayDetail paymentGatewayDetail) {
    try {
      Mac sha256Hmac = Mac.getInstance("HmacSHA256");
      SecretKeySpec secretKeySpec =
          new SecretKeySpec(
              paymentGatewayDetail.getConfig().getSecretKey().getBytes(StandardCharsets.UTF_8),
              "HmacSHA256");
      sha256Hmac.init(secretKeySpec);
      byte[] hash = sha256Hmac.doFinal(data.getBytes(StandardCharsets.UTF_8));
      return new String(Hex.encodeHex(hash));
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }
}
