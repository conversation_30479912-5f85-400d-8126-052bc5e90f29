package com.wexl.pallavi.dto;

import java.util.List;
import lombok.Builder;

public record FirstTerm6To8ReportDto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName, String address, String isoData, String academicYear, String studentId) {}

  @Builder
  public record Body(
      String name,
      String className,
      String rollNumber,
      String dateOfBirth,
      String mothersName,
      String fathersName,
      String teacherName,
      String sectionName,
      FirstTable firstTable,
      SecondTable secondTable,
      ThirdTable thirdTable,
      GradeTable gradeTable,
      Attendance attendance) {}

  @Builder
  public record FirstTable(
      String title,
      Column column,
      List<firstTableMarks> firstTableMarks,
      PercentageGrade percentageGrade) {}

  @Builder
  public record Column(String pt1, String nb1, String se1, String hye) {}

  @Builder
  public record firstTableMarks(
      String subject,
      String pt1,
      String nb1,
      String se1,
      String hye,
      String term1total,
      String term1Grade,
      String overallMarks,
      String overallGrade) {}

  @Builder
  public record PercentageGrade(
      String term1total, String term1Grade, String overallMarks, String overallGrade) {}

  @Builder
  public record SecondTable(List<SecondTableMarks> secondTableMarks) {}

  @Builder
  public record SecondTableMarks(
      String subjectName,
      String term1Marks,
      String term1Grade,
      String overallMarks,
      String overallGrade) {}

  @Builder
  public record ThirdTable(String title, List<ThirdTableMarks> thirdTableMarks) {}

  @Builder
  public record ThirdTableMarks(String subjectName, String term1Grade) {}

  @Builder
  public record GradeTable(String title) {}

  @Builder
  public record Attendance(
      Long workingDays, Long daysPresent, double attendancePer, String remarks) {}
}
